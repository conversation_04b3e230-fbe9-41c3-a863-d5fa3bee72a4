const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseService {
  constructor() {
    const dbPath = path.join(__dirname, '../data/permissions.db');
    this.db = new sqlite3.Database(dbPath);
    this.initializeTables();
  }

  initializeTables() {
    const tables = [
      // Workspaces table
      `CREATE TABLE IF NOT EXISTS workspaces (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        is_private BOOLEAN DEFAULT 0,
        created_on TEXT,
        last_checked TEXT DEFAULT CURRENT_TIMESTAMP,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        account_id TEXT,
        username TEXT,
        nickname TEXT,
        display_name TEXT,
        email TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Repositories table
      `CREATE TABLE IF NOT EXISTS repositories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        full_name TEXT,
        slug TEXT,
        workspace_id INTEGER,
        is_private BOOLEAN DEFAULT 0,
        created_on TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workspace_id) REFERENCES workspaces (id)
      )`,

      // Permissions table (current state)
      `CREATE TABLE IF NOT EXISTS permissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        repository_id INTEGER NOT NULL,
        workspace_id INTEGER NOT NULL,
        permission_type TEXT NOT NULL,
        granted_date TEXT,
        last_checked TEXT DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (repository_id) REFERENCES repositories (id),
        FOREIGN KEY (workspace_id) REFERENCES workspaces (id),
        UNIQUE(user_id, repository_id, permission_type)
      )`,

      // Audit log table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        action_type TEXT NOT NULL,
        user_id INTEGER,
        repository_id INTEGER,
        workspace_id INTEGER,
        permission_type TEXT,
        performed_by TEXT,
        details TEXT,
        timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (repository_id) REFERENCES repositories (id),
        FOREIGN KEY (workspace_id) REFERENCES workspaces (id)
      )`
    ];

    this.db.serialize(() => {
      tables.forEach(sql => {
        this.db.run(sql);
      });

      // Create indexes
      this.db.run('CREATE INDEX IF NOT EXISTS idx_permissions_user_repo ON permissions (user_id, repository_id)');
      this.db.run('CREATE INDEX IF NOT EXISTS idx_permissions_workspace ON permissions (workspace_id)');
      this.db.run('CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log (timestamp)');
      this.db.run('CREATE INDEX IF NOT EXISTS idx_audit_log_workspace ON audit_log (workspace_id)');
    });
  }

  // Save/update workspace
  saveWorkspace(workspaceData) {
    return new Promise((resolve, reject) => {
      this.db.run(
        `INSERT OR REPLACE INTO workspaces (uuid, slug, name, is_private, created_on, last_checked)
         VALUES (?, ?, ?, ?, ?, datetime('now'))`,
        [
          workspaceData.uuid,
          workspaceData.slug,
          workspaceData.name,
          workspaceData.is_private ? 1 : 0,
          workspaceData.created_on
        ],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID, changes: this.changes });
        }
      );
    });
  }

  // Log action to audit log
  logAction(actionType, userId, repositoryId, workspaceId, permissionType, performedBy, details = null) {
    return new Promise((resolve, reject) => {
      this.db.run(
        `INSERT INTO audit_log (action_type, user_id, repository_id, workspace_id, permission_type, performed_by, details, timestamp)
         VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
        [
          actionType,
          userId,
          repositoryId,
          workspaceId,
          permissionType,
          performedBy,
          details ? JSON.stringify(details) : null
        ],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });
  }

  // Get workspace ID by slug
  getWorkspaceId(workspaceSlug) {
    return new Promise((resolve, reject) => {
      this.db.get(
        'SELECT id FROM workspaces WHERE slug = ?',
        [workspaceSlug],
        (err, row) => {
          if (err) reject(err);
          else resolve(row ? row.id : null);
        }
      );
    });
  }

  // Get audit log for workspace
  getAuditLog(workspaceSlug, limit = 100) {
    return new Promise((resolve, reject) => {
      this.db.all(
        `SELECT
          al.*,
          u.display_name as user_name,
          u.nickname as user_nickname,
          r.name as repository_name,
          w.name as workspace_name
        FROM audit_log al
        LEFT JOIN users u ON al.user_id = u.id
        LEFT JOIN repositories r ON al.repository_id = r.id
        LEFT JOIN workspaces w ON al.workspace_id = w.id
        WHERE w.slug = ?
        ORDER BY al.timestamp DESC
        LIMIT ?`,
        [workspaceSlug, limit],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  // Close database connection
  close() {
    this.db.close();
  }
}

module.exports = DatabaseService;
