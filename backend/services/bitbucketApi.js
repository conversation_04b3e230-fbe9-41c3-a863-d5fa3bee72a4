const axios = require('axios');
const DatabaseService = require('./database');

class BitbucketAPI {
  constructor() {
    this.baseURL = 'https://api.bitbucket.org/2.0';
    this.token = process.env.BITBUCKET_API_TOKEN;
    this.db = new DatabaseService();
  }

  getClient() {
    if (!this.token) {
      throw new Error('BITBUCKET_API_TOKEN environment variable is required');
    }

    if (!this.client) {
      // Bitbucket uses Basic Auth: username:app_password
      const username = process.env.USERNAME || 'Hennot'; // Bitbucket username from env
      const credentials = Buffer.from(`${username}:${this.token}`).toString('base64');

      this.client = axios.create({
        baseURL: this.baseURL,
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
    }

    return this.client;
  }

  // Get user workspaces (all pages)
  async getUserWorkspaces() {
    try {
      const client = this.getClient();
      let allWorkspaces = [];
      let nextUrl = '/workspaces?pagelen=100'; // Maximum page size

      while (nextUrl) {
        const response = await client.get(nextUrl);
        const data = response.data;

        if (data.values) {
          allWorkspaces = allWorkspaces.concat(data.values);
        }

        // Next page
        nextUrl = data.next ? data.next.replace('https://api.bitbucket.org/2.0', '') : null;
      }

      // Save workspaces to database
      for (const workspace of allWorkspaces) {
        try {
          await this.db.saveWorkspace(workspace);
        } catch (dbError) {
          console.error('Error saving workspace:', dbError.message);
        }
      }

      return {
        values: allWorkspaces,
        size: allWorkspaces.length
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to load workspaces');
    }
  }

  // Get workspace members (all pages)
  async getWorkspaceMembers(workspace) {
    try {
      const client = this.getClient();
      let allMembers = [];
      let nextUrl = `/workspaces/${workspace}/members?pagelen=100`;

      while (nextUrl) {
        const response = await client.get(nextUrl);
        const data = response.data;

        if (data.values) {
          allMembers = allMembers.concat(data.values);
        }

        // Next page
        nextUrl = data.next ? data.next.replace('https://api.bitbucket.org/2.0', '') : null;
      }

      return {
        values: allMembers,
        size: allMembers.length
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to load workspace members');
    }
  }

  // Get workspace repositories (all pages)
  async getWorkspaceRepositories(workspace) {
    try {
      const client = this.getClient();
      let allRepositories = [];
      let nextUrl = `/repositories/${workspace}?pagelen=100`;

      while (nextUrl) {
        const response = await client.get(nextUrl);
        const data = response.data;

        if (data.values) {
          allRepositories = allRepositories.concat(data.values);
        }

        // Next page
        nextUrl = data.next ? data.next.replace('https://api.bitbucket.org/2.0', '') : null;
      }

      return {
        values: allRepositories,
        size: allRepositories.length
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to load repositories');
    }
  }

  // Get all repository permissions in workspace (all pages)
  async getWorkspaceRepositoryPermissions(workspace) {
    try {
      const client = this.getClient();
      let allPermissions = [];
      let nextUrl = `/workspaces/${workspace}/permissions/repositories?pagelen=100`;

      while (nextUrl) {
        const response = await client.get(nextUrl);
        const data = response.data;

        if (data.values) {
          allPermissions = allPermissions.concat(data.values);
        }

        // Next page
        nextUrl = data.next ? data.next.replace('https://api.bitbucket.org/2.0', '') : null;
      }

      return {
        values: allPermissions,
        size: allPermissions.length
      };
    } catch (error) {
      throw this.handleError(error, 'Failed to load repository permissions');
    }
  }

  // Get specific repository user permissions
  async getRepositoryUserPermissions(workspace, repoSlug) {
    try {
      const client = this.getClient();
      const response = await client.get(`/repositories/${workspace}/${repoSlug}/permissions-config/users`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to load repository user permissions');
    }
  }

  // Kasutaja õiguse eemaldamine repositooriumist
  async removeUserPermissionFromRepository(workspace, repoSlug, userId, permissionType = 'unknown') {
    try {
      const client = this.getClient();

      // Esmalt hangi kasutaja ja repo info
      const [userInfo, repoInfo] = await Promise.all([
        this.getUserInfo(userId).catch(() => ({ uuid: userId, display_name: 'Tundmatu kasutaja' })),
        this.getRepositoryInfo(workspace, repoSlug).catch(() => ({ uuid: repoSlug, name: repoSlug }))
      ]);

      // Eemalda õigus Bitbucket'ist
      const response = await client.delete(`/repositories/${workspace}/${repoSlug}/permissions-config/users/${userId}`);

      // Log to database
      try {
        const workspaceId = await this.db.getWorkspaceId(workspace);

        if (workspaceId) {
          // Log action
          await this.db.logAction(
            'permission_removed',
            null, // user_id - we don't have it in our DB yet
            null, // repository_id - we don't have it in our DB yet
            workspaceId,
            permissionType,
            'system',
            {
              workspace: workspace,
              repository: repoSlug,
              user: userInfo.display_name || userInfo.nickname,
              user_id: userId,
              removed_at: new Date().toISOString(),
              success: true
            }
          );

          console.log(`[${new Date().toISOString()}] Successfully removed user ${userId} permission from repository ${workspace}/${repoSlug}`);
        }
      } catch (dbError) {
        console.error('Error logging to database:', dbError.message);
      }

      return {
        success: true,
        message: 'User permission successfully removed',
        user: userInfo.display_name || userInfo.nickname,
        repository: repoInfo.name
      };
    } catch (error) {
      // Log failed attempt to database
      try {
        const workspaceId = await this.db.getWorkspaceId(workspace);
        if (workspaceId) {
          await this.db.logAction(
            'permission_removal_failed',
            null,
            null,
            workspaceId,
            permissionType,
            'system',
            {
              workspace: workspace,
              repository: repoSlug,
              user_id: userId,
              error: error.message,
              failed_at: new Date().toISOString()
            }
          );
        }
      } catch (dbError) {
        console.error('Error logging failed attempt:', dbError.message);
      }

      throw this.handleError(error, 'Failed to remove user permission');
    }
  }
  
  // Kasutaja õiguse muutmine repositooriumis
  async updateUserRepositoryPermission(workspace, repoSlug, userId, permission) {
    try {
      const client = this.getClient();

      // Esmalt hangi kasutaja ja repo info
      const [userInfo, repoInfo] = await Promise.all([
        this.getUserInfo(userId).catch(() => ({ uuid: userId, display_name: 'Tundmatu kasutaja' })),
        this.getRepositoryInfo(workspace, repoSlug).catch(() => ({ uuid: repoSlug, name: repoSlug }))
      ]);

      // Muuda kasutaja õigusi Bitbucket'is
      const response = await client.put(`/repositories/${workspace}/${repoSlug}/permissions-config/users/${userId}`, {
        permission: permission
      });

      // Log to database
      try {
        const workspaceId = await this.db.getWorkspaceId(workspace);

        if (workspaceId) {
          // Log action
          await this.db.logAction(
            'permission_updated',
            null, 
            null, 
            workspaceId,
            permission,
            'system',
            {
              workspace: workspace,
              repository: repoSlug,
              user: userInfo.display_name || userInfo.nickname,
              user_id: userId,
              new_permission: permission,
              updated_at: new Date().toISOString(),
              success: true
            }
          );

          console.log(`[${new Date().toISOString()}] Successfully updated user ${userId} permission to '${permission}' for repository ${workspace}/${repoSlug}`);
        }
      } catch (dbError) {
        console.error('Error logging to database:', dbError.message);
      }

      return {
        success: true,
        message: 'User permission successfully updated',
        user: userInfo.display_name || userInfo.nickname,
        repository: repoInfo.name,
        permission: permission
      };
    } catch (error) {
      // Log failed attempt to database
      try {
        const workspaceId = await this.db.getWorkspaceId(workspace);
        if (workspaceId) {
          await this.db.logAction(
            'permission_update_failed',
            null,
            null,
            workspaceId,
            permission,
            'system',
            {
              workspace: workspace,
              repository: repoSlug,
              user_id: userId,
              attempted_permission: permission,
              error: error.message,
              failed_at: new Date().toISOString()
            }
          );
        }
      } catch (dbError) {
        console.error('Error logging failed attempt:', dbError.message);
      }

      throw this.handleError(error, 'Failed to update user permission');
    }
  }

  // Workspace'i projektid (all pages)
  async getWorkspaceProjects(workspace) {
    try {
      const client = this.getClient();
      let allProjects = [];
      let nextUrl = `/workspaces/${workspace}/projects?pagelen=100`;

      while (nextUrl) {
        const response = await client.get(nextUrl);
        const data = response.data;

        if (data.values) {
          allProjects = allProjects.concat(data.values);
        }

        // Next page
        nextUrl = data.next ? data.next.replace('https://api.bitbucket.org/2.0', '') : null;
      }

      return {
        values: allProjects,
        size: allProjects.length
      };
    } catch (error) {
      throw this.handleError(error, 'Projektide laadimine ebaõnnestus');
    }
  }

  // Get full user information
  async getUserInfo(userIdOrUuid) {
    try {
      const client = this.getClient();
      // Clean the UUID if it has curly braces
      const cleanId = userIdOrUuid.replace(/[{}]/g, '');
      const response = await client.get(`/users/${cleanId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Failed to load user information');
    }
  }

  // Workspace info
  async getWorkspaceInfo(workspace) {
    try {
      const client = this.getClient();
      const response = await client.get(`/workspaces/${workspace}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Workspace info laadimine ebaõnnestus');
    }
  }

  // Kasutaja info
  async getUserInfo(userId) {
    try {
      const client = this.getClient();
      const response = await client.get(`/users/${userId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Kasutaja info laadimine ebaõnnestus');
    }
  }

  // Repositooriumi info
  async getRepositoryInfo(workspace, repoSlug) {
    try {
      const client = this.getClient();
      const response = await client.get(`/repositories/${workspace}/${repoSlug}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error, 'Repositooriumi info laadimine ebaõnnestus');
    }
  }

  // Vigade käsitlemine
  handleError(error, defaultMessage) {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.response.data?.message || defaultMessage;
      
      switch (status) {
        case 401:
          return new Error('Autentimine ebaõnnestus. Kontrolli API võtit.');
        case 403:
          return new Error('Ligipääs keelatud. Kontrolli API võtme õigusi.');
        case 404:
          return new Error('Ressurss ei leitud. Kontrolli workspace nime.');
        case 429:
          return new Error('Liiga palju päringuid. Proovi hiljem uuesti.');
        default:
          return new Error(`${defaultMessage}: ${message}`);
      }
    } else if (error.request) {
      return new Error('Võrguühenduse viga. Kontrolli internetiühendust.');
    } else {
      return new Error(`${defaultMessage}: ${error.message}`);
    }
  }
}

module.exports = BitbucketAPI;
