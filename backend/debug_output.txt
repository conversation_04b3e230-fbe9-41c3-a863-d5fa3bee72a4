🔍 Fetching workspaces to find users...
✅ Found 10 workspaces

🔍 Checking workspace avatar structure:
Workspace object keys: [
  'type',
  'uuid',
  'name',
  'slug',
  'is_private',
  'is_privacy_enforced',
  'links',
  'created_on',
  'forking_mode'
]
Workspace links: {
  avatar: {
    href: 'https://bitbucket.org/workspaces/ovaal/avatar/?ts=1681810367'
  },
  hooks: { href: 'https://api.bitbucket.org/2.0/workspaces/ovaal/hooks' },
  html: { href: 'https://bitbucket.org/ovaal/' },
  html_overview: { href: 'https://bitbucket.org/ovaal/workspace/overview/' },
  members: { href: 'https://api.bitbucket.org/2.0/workspaces/ovaal/members' },
  owners: {
    href: 'https://api.bitbucket.org/2.0/workspaces/ovaal/members?q=permission%3D%22owner%22'
  },
  projects: { href: 'https://api.bitbucket.org/2.0/workspaces/ovaal/projects' },
  repositories: { href: 'https://api.bitbucket.org/2.0/repositories/ovaal' },
  snippets: { href: 'https://api.bitbucket.org/2.0/snippets/ovaal' },
  self: { href: 'https://api.bitbucket.org/2.0/workspaces/ovaal' }
}
✅ Workspace avatar URL: https://bitbucket.org/workspaces/ovaal/avatar/?ts=1681810367

🔍 Fetching workspace members...
✅ Found 5 members

🔍 Member 1 user object: {
  "display_name": "Henno Täht",
  "links": {
    "self": {
      "href": "https://api.bitbucket.org/2.0/users/%7B111e0832-7787-4e38-ab90-f8775b1673eb%7D"
    },
    "avatar": {
      "href": "https://secure.gravatar.com/avatar/66b7ec1f3ae9fca282787c589509bf3c?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FHT-3.png"
    },
    "html": {
      "href": "https://bitbucket.org/%7B111e0832-7787-4e38-ab90-f8775b1673eb%7D/"
    }
  },
  "type": "user",
  "uuid": "{111e0832-7787-4e38-ab90-f8775b1673eb}",
  "account_id": "557058:1259bd41-9499-48ce-be69-cb3f858c1ce9",
  "nickname": "hennot"
}

🔍 Fetching full user info for: Henno Täht
❌ Error fetching user info: Request failed with status code 404

🔍 Member 2 user object: {
  "display_name": "Arnold Grosberg",
  "links": {
    "self": {
      "href": "https://api.bitbucket.org/2.0/users/%7Bc585389b-93bf-4e17-9d1b-448ca389c6a9%7D"
    },
    "avatar": {
      "href": "https://secure.gravatar.com/avatar/5f435eb35ef39205a2d6fc9124ae68f7?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAG-3.png"
    },
    "html": {
      "href": "https://bitbucket.org/%7Bc585389b-93bf-4e17-9d1b-448ca389c6a9%7D/"
    }
  },
  "type": "user",
  "uuid": "{c585389b-93bf-4e17-9d1b-448ca389c6a9}",
  "account_id": "63450c74cba49e29097103a6",
  "nickname": "Arnold Grosberg"
}

🔍 Fetching full user info for: Arnold Grosberg
❌ Error fetching user info: Request failed with status code 404

🔍 Member 3 user object: {
  "display_name": "Mihkel Kougia",
  "links": {
    "self": {
      "href": "https://api.bitbucket.org/2.0/users/%7B0600359a-6ddd-4193-b94b-4baf8ae04eec%7D"
    },
    "avatar": {
      "href": "https://secure.gravatar.com/avatar/cb69f98da8fa63aea33c54c048542f82?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FMK-6.png"
    },
    "html": {
      "href": "https://bitbucket.org/%7B0600359a-6ddd-4193-b94b-4baf8ae04eec%7D/"
    }
  },
  "type": "user",
  "uuid": "{0600359a-6ddd-4193-b94b-4baf8ae04eec}",
  "account_id": "712020:3cd514d1-4143-4221-8fbd-434210ff9029",
  "nickname": "Mihkel Kougia"
}

🔍 Fetching full user info for: Mihkel Kougia
❌ Error fetching user info: Request failed with status code 404
