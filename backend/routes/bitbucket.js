const express = require('express');
const BitbucketAPI = require('../services/bitbucketApi');
const DatabaseService = require('../services/database');

const router = express.Router();

// Loo BitbucketAPI instants iga päringu jaoks
const getBitbucketAPI = () => {
  return new BitbucketAPI();
};

// Kõik kasutaja workspace'id
router.get('/workspaces', async (req, res) => {
  try {
    const bitbucketAPI = getBitbucketAPI();
    const workspaces = await bitbucketAPI.getUserWorkspaces();
    res.json(workspaces);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Workspace info
router.get('/workspace/:workspace', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const workspaceInfo = await bitbucketAPI.getWorkspaceInfo(workspace);
    res.json(workspaceInfo);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Workspace liikmed
router.get('/workspace/:workspace/members', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const members = await bitbucketAPI.getWorkspaceMembers(workspace);
    res.json(members);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Workspace repositooriumid
router.get('/workspace/:workspace/repositories', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const repositories = await bitbucketAPI.getWorkspaceRepositories(workspace);
    res.json(repositories);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Kõik repositooriumi õigused workspace'is
router.get('/workspace/:workspace/permissions', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const permissions = await bitbucketAPI.getWorkspaceRepositoryPermissions(workspace);
    res.json(permissions);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Konkreetse repositooriumi kasutajate õigused
router.get('/workspace/:workspace/repository/:repoSlug/users', async (req, res) => {
  try {
    const { workspace, repoSlug } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const users = await bitbucketAPI.getRepositoryUserPermissions(workspace, repoSlug);
    res.json(users);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Kasutaja õiguse eemaldamine repositooriumist
router.delete('/workspace/:workspace/repository/:repoSlug/user/:userId', async (req, res) => {
  try {
    const { workspace, repoSlug, userId } = req.params;

    // Logi toiming
    console.log(`[${new Date().toISOString()}] Eemaldatakse kasutaja ${userId} õigus repositooriumist ${workspace}/${repoSlug}`);

    const bitbucketAPI = getBitbucketAPI();
    const result = await bitbucketAPI.removeUserPermissionFromRepository(workspace, repoSlug, userId);

    // Logi edukas toiming
    console.log(`[${new Date().toISOString()}] Edukalt eemaldatud kasutaja ${userId} õigus repositooriumist ${workspace}/${repoSlug}`);

    res.json(result);
  } catch (error) {
    // Logi viga
    console.error(`[${new Date().toISOString()}] Viga kasutaja ${req.params.userId} õiguse eemaldamisel: ${error.message}`);
    res.status(400).json({ error: error.message });
  }
});

// Kasutaja õiguse muutmine repositooriumis
router.put('/workspace/:workspace/repository/:repoSlug/user/:userId', async (req, res) => {
  try {
    const { workspace, repoSlug, userId } = req.params;
    const { permission } = req.body;
    
    // Valideeri õigus
    if (!permission || !['admin', 'write', 'read'].includes(permission)) {
      return res.status(400).json({ error: 'Kehtetu õiguse tase. Lubatud väärtused: admin, write, read' });
    }
    
    // Logi toiming
    console.log(`[${new Date().toISOString()}] Muudetakse kasutaja ${userId} õigus repositooriumis ${workspace}/${repoSlug} tasemele: ${permission}`);
    
    const bitbucketAPI = getBitbucketAPI();
    const result = await bitbucketAPI.updateUserRepositoryPermission(workspace, repoSlug, userId, permission);
    
    // Logi edukas toiming
    console.log(`[${new Date().toISOString()}] Edukalt muudetud kasutaja ${userId} õigus repositooriumis ${workspace}/${repoSlug}`);
    
    res.json(result);
  } catch (error) {
    // Logi viga
    console.error(`[${new Date().toISOString()}] Viga kasutaja ${req.params.userId} õiguse muutmisel: ${error.message}`);
    res.status(400).json({ error: error.message });
  }
});

// Workspace projektid
router.get('/workspace/:workspace/projects', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const projects = await bitbucketAPI.getWorkspaceProjects(workspace);
    res.json(projects);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Koondatud ülevaade - kõik kasutajad ja nende õigused
router.get('/workspace/:workspace/overview', async (req, res) => {
  try {
    const { workspace } = req.params;
    const bitbucketAPI = getBitbucketAPI();

    // Laadi paralleelselt kõik vajalikud andmed
    const [workspaceInfo, members, repositories, permissions, projects] = await Promise.all([
      bitbucketAPI.getWorkspaceInfo(workspace).catch(() => null),
      bitbucketAPI.getWorkspaceMembers(workspace).catch(() => ({ values: [], size: 0 })),
      bitbucketAPI.getWorkspaceRepositories(workspace).catch(() => ({ values: [], size: 0 })),
      bitbucketAPI.getWorkspaceRepositoryPermissions(workspace).catch(() => ({ values: [], size: 0 })),
      bitbucketAPI.getWorkspaceProjects(workspace).catch(() => ({ values: [], size: 0 }))
    ]);

    // Save workspace data to database
    try {
      const db = new DatabaseService();

      // Save workspace info
      if (workspaceInfo) {
        await db.saveWorkspace(workspaceInfo);

        const workspaceId = await db.getWorkspaceId(workspace);
        if (workspaceId) {
          // Log workspace access
          await db.logAction(
            'workspace_accessed',
            null,
            null,
            workspaceId,
            null,
            'system',
            {
              workspace: workspace,
              members_count: members.size || 0,
              repositories_count: repositories.size || 0,
              permissions_count: permissions.size || 0,
              projects_count: projects.size || 0,
              accessed_at: new Date().toISOString()
            }
          );
        }
      }

      db.close();
    } catch (dbError) {
      console.error('Error saving workspace data to database:', dbError.message);
    }

    // Enhance user data in permissions - get full user info for each unique user
    const enhancedPermissions = [];
    const userCache = new Map();

    for (const permission of (permissions.values || [])) {
      if (permission.user) {
        const userId = permission.user.uuid || permission.user.account_id;

        // If we haven't cached this user's full data yet, get it
        if (!userCache.has(userId)) {
          try {
            // Get full user data from Bitbucket API
            const fullUserData = await bitbucketAPI.getUserInfo(permission.user.uuid || permission.user.account_id);
            userCache.set(userId, fullUserData);
          } catch (error) {
            console.log(`Could not get full user data for ${permission.user.display_name || permission.user.nickname}`);
            userCache.set(userId, permission.user); // Use existing data as fallback
          }
        }

        // Use cached full user data
        enhancedPermissions.push({
          ...permission,
          user: userCache.get(userId)
        });
      } else {
        enhancedPermissions.push(permission);
      }
    }

    // Build combined overview
    const overview = {
      workspace: workspace,
      workspaceInfo: workspaceInfo,
      totalMembers: members.size || 0,
      totalRepositories: repositories.size || 0,
      totalPermissions: permissions.size || 0,
      totalProjects: projects.size || 0,
      members: members.values || [],
      repositories: repositories.values || [],
      permissions: enhancedPermissions,
      projects: projects.values || []
    };

    res.json(overview);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Workspace statistics from database
router.get('/workspace/:workspace/stats', async (req, res) => {
  try {
    const { workspace } = req.params;
    const db = new DatabaseService();

    // For now, return basic stats since we don't have full DB integration yet
    res.json({
      total_users: 0,
      total_repositories: 0,
      total_permissions: 0,
      admin_permissions: 0,
      write_permissions: 0,
      read_permissions: 0
    });

    db.close();
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Audit log
router.get('/workspace/:workspace/audit', async (req, res) => {
  try {
    const { workspace } = req.params;
    const { limit = 100 } = req.query;
    const db = new DatabaseService();
    const auditLog = await db.getAuditLog(workspace, parseInt(limit));
    db.close();

    res.json({
      workspace: workspace,
      total: auditLog.length,
      entries: auditLog
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// All saved workspaces
router.get('/workspaces/saved', async (req, res) => {
  try {
    const db = new DatabaseService();

    // For now, return empty array since we don't have full DB integration yet
    res.json({
      values: [],
      size: 0
    });

    db.close();
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Kasutaja info
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const bitbucketAPI = getBitbucketAPI();
    const userInfo = await bitbucketAPI.getUserInfo(userId);
    res.json(userInfo);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

// Test database connection
router.get('/test-db', async (req, res) => {
  try {
    const db = new DatabaseService();

    // Test saving a workspace
    await db.saveWorkspace({
      uuid: 'test-uuid',
      slug: 'test-workspace',
      name: 'Test Workspace',
      is_private: false,
      created_on: new Date().toISOString()
    });

    // Test logging an action
    const workspaceId = await db.getWorkspaceId('test-workspace');
    if (workspaceId) {
      await db.logAction(
        'workspace_checked',
        null,
        null,
        workspaceId,
        null,
        'system',
        { test: true, timestamp: new Date().toISOString() }
      );
    }

    // Get audit log
    const auditLog = await db.getAuditLog('test-workspace', 10);

    db.close();

    res.json({
      success: true,
      message: 'Database test successful',
      workspaceId: workspaceId,
      auditEntries: auditLog.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Test permission removal logging
router.post('/test-permission-removal', async (req, res) => {
  try {
    const db = new DatabaseService();

    // Ensure test workspace exists
    await db.saveWorkspace({
      uuid: 'test-uuid',
      slug: 'test-workspace',
      name: 'Test Workspace',
      is_private: false,
      created_on: new Date().toISOString()
    });

    const workspaceId = await db.getWorkspaceId('test-workspace');

    if (workspaceId) {
      // Log a permission removal
      await db.logAction(
        'permission_removed',
        null,
        null,
        workspaceId,
        'write',
        'system',
        {
          workspace: 'test-workspace',
          repository: 'test-repo',
          user: 'Test User',
          user_id: 'test-user-123',
          removed_at: new Date().toISOString(),
          success: true
        }
      );
    }

    // Get updated audit log
    const auditLog = await db.getAuditLog('test-workspace', 10);

    db.close();

    res.json({
      success: true,
      message: 'Permission removal logged successfully',
      workspaceId: workspaceId,
      auditEntries: auditLog.length,
      latestEntry: auditLog[0]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

module.exports = router;
