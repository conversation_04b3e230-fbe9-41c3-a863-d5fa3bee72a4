#!/usr/bin/env node

// Simple test to verify what data the frontend is receiving
const axios = require('axios');

async function testFrontendData() {
  try {
    console.log('🔍 Testing frontend data flow...');
    
    // Test the backend API endpoint that frontend calls
    console.log('\n1. Testing backend workspaces endpoint...');
    const response = await axios.get('http://localhost:3002/api/bitbucket/workspaces');
    
    console.log('✅ API Response Status:', response.status);
    console.log('✅ API Response Data Keys:', Object.keys(response.data));
    
    if (response.data.values && response.data.values.length > 0) {
      console.log('✅ Found', response.data.values.length, 'workspaces');
      
      // Check first 3 workspaces for avatar data
      console.log('\n2. Avatar URL analysis and Full First Workspace Object:');
      
      // Log the full first workspace object
      if (response.data.values && response.data.values[0]) {
        console.log('\n--- Full First Workspace Object ---');
        console.log(JSON.stringify(response.data.values[0], null, 2));
        console.log('--- End of Full First Workspace Object ---');
      }

      response.data.values.slice(0, 3).forEach((workspace, index) => {
        console.log(`\n--- Workspace ${index + 1}: ${workspace.name} ---`);
        console.log('UUID:', workspace.uuid);
        console.log('Slug:', workspace.slug);
        console.log('Links object:', workspace.links ? 'Present' : 'Missing');
        console.log('Avatar URL:', workspace.links?.avatar?.href || 'NOT FOUND');
        
        if (workspace.links?.avatar?.href) {
          console.log('✅ Avatar URL structure looks correct');
        } else {
          console.log('❌ No avatar URL found!');
        }
      });
      
      // Test if avatar URLs are accessible
      console.log('\n3. Testing avatar URL accessibility...');
      const firstWorkspace = response.data.values[0];
      if (firstWorkspace?.links?.avatar?.href) {
        try {
          const avatarResponse = await axios.head(firstWorkspace.links.avatar.href);
          console.log('✅ Avatar URL is accessible:', avatarResponse.status);
        } catch (avatarError) {
          console.log('❌ Avatar URL failed:', avatarError.message);
        }
      }
      
    } else {
      console.log('❌ No workspaces found in API response');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFrontendData();
