// Test script to verify avatar URL extraction

console.log('Starting avatar URL extraction test...');

// Sample avatar URLs from our debug output
const testUrls = [
  "https://secure.gravatar.com/avatar/66b7ec1f3ae9fca282787c589509bf3c?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FHT-3.png",
  "https://secure.gravatar.com/avatar/5f435eb35ef39205a2d6fc9124ae68f7?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAG-3.png",
  "https://secure.gravatar.com/avatar/cb69f98da8fa63aea33c54c048542f82?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FMK-6.png"
];

// Extract Atlassian fallback function (same as in Avatar component)
function extractAtlassianFallback(gravatarUrl) {
  if (!gravatarUrl || !gravatarUrl.includes('gravatar.com')) return null;
  
  try {
    const url = new URL(gravatarUrl);
    const fallbackParam = url.searchParams.get('d');
    if (fallbackParam && fallbackParam.includes('avatar-management--avatars')) {
      return decodeURIComponent(fallbackParam);
    }
  } catch (e) {
    console.log('Failed to extract Atlassian fallback:', e);
  }
  return null;
}

console.log('🧪 Testing avatar URL extraction...\n');

testUrls.forEach((url, index) => {
  console.log(`Test ${index + 1}:`);
  console.log(`Original: ${url}`);
  
  const extracted = extractAtlassianFallback(url);
  console.log(`Extracted: ${extracted}`);
  console.log('---');
});

console.log('\n✅ Extraction test complete!');
