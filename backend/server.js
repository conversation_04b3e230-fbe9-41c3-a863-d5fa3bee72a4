const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const bitbucketRoutes = require('./routes/bitbucket');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/bitbucket', bitbucketRoutes);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Bitbucket Permission Manager API' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Midagi läks valesti!', 
    message: err.message 
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint ei leitud' });
});

app.listen(PORT, () => {
  console.log(`Server töötab pordil ${PORT}`);
  console.log(`API saadaval: http://localhost:${PORT}/api`);
});
