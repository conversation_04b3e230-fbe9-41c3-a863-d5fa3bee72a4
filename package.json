{"name": "bitbucket-user-permission-manager", "version": "1.0.0", "description": "Bitbucket kasutajate <PERSON>e halda<PERSON> tööriist", "main": "backend/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["bitbucket", "permissions", "management", "react", "nodejs"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"node-fetch": "^3.3.2"}}