# Bitbucket Ligipääsude Haldus

Veebipõhine tööriist Bitbucket kasutajate õiguste haldamiseks. Võimaldab vaadata kõiki kasutajaid, kellel on ligipääs ettevõtte repositooriumidele, ja eemaldada nende õigusi.

## Funktsioonid

- 📋 Ülevaade kõikidest kasutajatest ja nende õigustest
- 🗑️ Kasutajate õiguste eemaldamine repositooriumidest
- 🔍 Kasutajate ja repositooriumide filtreerimine ja otsing
- 🏢 Workspace, projekti ja repository tasandi õiguste haldus
- 📊 **Statistika kaardid**: Interaktiivsed ülevaated tööruumidest ja repositooriumidest
- ✅ Kinnituse dialoogid ohutu kasutamise jaoks
- 🎯 **Klõpsatavad statistikad**: Filtreeri andmeid statistika kaartide kaudu
- 💾 **Andmete cache'imine**: Kiire navigeerimine workspace'ide vahel
- 📁 **Repository haldus**: Ülevaade kõikidest repositooriumidest workspace'ide lõikes

### Uued Funktsioonid (2025)
- ✨ **Interaktiivsed statistika kaardid**: 5 klõpsatavat kaarti (Total, Public, Private, Filtered, Repositories)
- 🎨 **Visuaalne tagasiside**: Aktiivse oleku indikaatorid ja hover efektid
- 🔄 **Automaatne andmete laadimine**: Populeerib statistikad automaatselt
- 🚫 **Duplikaatide kõrvaldamine**: Õige repositooriumide loendamine cross-workspace'ides
- 📈 **Detailne debug info**: Paremad võimed probleemide diagnoosimiseks

## Tehnoloogiad

- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: Node.js + Express
- **API**: Bitbucket Cloud REST API

## Seadistamine

### 1. Installeeri sõltuvused
```bash   
npm run install-all
```

### 2. Seadista keskkonnamuutujad
Kopeeri näidisfail ja lisa oma API võti:
```bash
cp backend/.env.example backend/.env
```

Muuda `backend/.env` failis:
```
BITBUCKET_API_TOKEN=sinu_tegelik_api_võti
PORT=3001
```

### 3. Käivita rakendus
```bash
npm run dev
```

Rakendus avaneb aadressil: http://localhost:5173

## API Võti

Kasuta Bitbucket App Password'i järgmiste õigustega:
- Account: Read
- Workspace membership: Read, Write
- Repositories: Admin
- Projects: Read

## Kasutamine

### Põhifunktsioonid
1. Ava rakendus brauseris (http://localhost:5173)
2. Klõpsa "Load Workspaces" et laadida tööruumid
3. Vaata ülevaadet statistika kaartidel
4. Kasuta klõpsatavaid statistika kaarte filtreerimiseks
5. Eemalda õigused "Eemalda" nuppudega

### Statistika Kaardid
- **Total Workspaces**: Näitab kõiki tööruume ja filtreerib kõike
- **Public**: Filtreerib ainult avalikud tööruumid  
- **Private**: Filtreerib ainult privaatvused tööruumid
- **Filtered**: Näitab praeguste filtrite tulemusi
- **Total Repositories**: Laeb ja näitab kõiki repositooriumeid

### Navigeerimine
- Klõpsa statistika kaardil filtreerimiseks
- Klõpsa sama kaarti uuesti filtri eemaldamiseks
- Kasuta X nuppu aktiivsuse indikaatoris filtri kustutamiseks
- Kasuta otsinguriba täpsemaks filtreerimiseks

## Turvalisus

⚠️ **Hoiatus**: See tööriist võimaldab eemaldada kasutajate ligipääsu repositooriumidele. Kasuta ettevaatlikult!

- Kõik toimingud nõuavad kinnitust
- API võti hoitakse turvaliselt backend'is
- Logitakse kõik õiguste muudatused
