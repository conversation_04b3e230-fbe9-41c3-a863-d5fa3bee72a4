// Import fs and path modules using ES module syntax
import fs from 'fs';
import path from 'path';

// Define __dirname for ES modules
const __dirname = path.dirname(new URL(import.meta.url).pathname);

// Main function
async function fixMain() {
  console.log('Starting main.jsx fix...');
  
  // Path to main.jsx
  const mainJsxPath = path.join(__dirname, 'src', 'main.jsx');
  
  // Create minimal main.jsx content
  const content = `// filepath: /Users/<USER>/TAK24/Tööd/praktika/bitbucket-user-permission-manager/frontend/src/main.jsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'

// Simple App component
function SimpleApp() {
  return (
    <div style={{ 
      maxWidth: '800px', 
      margin: '40px auto', 
      padding: '20px', 
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h1 style={{ color: '#2563eb' }}>Bitbucket User Permission Manager</h1>
      <p>Application is loading...</p>
      <p>Current time: {new Date().toLocaleTimeString()}</p>
      <div style={{ 
        backgroundColor: '#f1f5f9', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px' 
      }}>
        <h2>Debug Information</h2>
        <p>React version: {React.version}</p>
        <p>React DOM root element: document.getElementById('root')</p>
      </div>
    </div>
  )
}

// Log for debugging
console.log('main.jsx executing at', new Date().toISOString());

// Find root element
const rootElement = document.getElementById('root');
console.log('Root element found:', rootElement);

// Render with error handling
try {
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <SimpleApp />
    </React.StrictMode>,
  );
  console.log('React render complete');
} catch (error) {
  console.error('Error rendering React:', error);
  
  // Show error on page
  if (rootElement) {
    rootElement.innerHTML = \`
      <div style="color: red; padding: 20px; font-family: sans-serif;">
        <h1>React Error</h1>
        <p>\${error.message}</p>
        <pre>\${error.stack}</pre>
      </div>
    \`;
  } else {
    document.body.innerHTML = \`
      <div style="color: red; padding: 20px; font-family: sans-serif;">
        <h1>React Root Element Error</h1>
        <p>Could not find element with id "root"</p>
      </div>
    \`;
  }
}
`;

  // Write file
  try {
    fs.writeFileSync(mainJsxPath, content, 'utf8');
    console.log(`Successfully updated ${mainJsxPath}`);
    
    // Also read index.html to verify
    const indexHtmlPath = path.join(__dirname, 'index.html');
    const indexContent = fs.readFileSync(indexHtmlPath, 'utf8');
    
    if (indexContent.includes('id="root"')) {
      console.log('index.html contains a root element with id="root"');
    } else {
      console.error('WARNING: index.html does not contain an element with id="root"');
    }
    
    console.log('\nFix complete. Now run the following commands:');
    console.log('cd /Users/<USER>/TAK24/Tööd/praktika/bitbucket-user-permission-manager/frontend');
    console.log('npm run dev\n');
    
  } catch (error) {
    console.error('Error writing file:', error);
  }
}

// Run main function
fixMain();
