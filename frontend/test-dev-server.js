import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'vite';
import process from 'process'; // Import process to use process.exit

// Get current directory
const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function main() {
  try {
    console.log('Starting Vite server in debug mode...');
    
    // Check that index.html exists
    const indexPath = path.resolve(__dirname, 'index.html');
    if (!fs.existsSync(indexPath)) {
      console.error(`Error: index.html not found at ${indexPath}`);
      // Throw an error to terminate the script if index.html is missing
      throw new Error(`index.html not found at ${indexPath}`);
    }
    console.log(`Found index.html at ${indexPath}`);
    
    // Check that main.jsx exists
    const mainJsxPath = path.resolve(__dirname, 'src/main.jsx');
    if (!fs.existsSync(mainJsxPath)) {
      console.error(`Error: main.jsx not found at ${mainJsxPath}`);
      // Throw an error to terminate the script if main.jsx is missing
      throw new Error(`main.jsx not found at ${mainJsxPath}`);
    }
    console.log(`Found main.jsx at ${mainJsxPath}`);
    
    // Create Vite server
    const server = await createServer({
      configFile: path.resolve(__dirname, 'vite.config.js'),
      root: __dirname,
      server: {
        port: 5177,
        strictPort: true,
        hmr: { overlay: false }, // Disable HMR overlay to see console errors better
      },
      logLevel: 'info',
    });
    
    // Start server
    await server.listen();
    
    const info = server.config.server;
    console.log(`\nVite debug server running at:`);
    console.log(`➜ Local: http://localhost:${info.port}/`);
    console.log(`Press Ctrl+C to stop the server\n`);
    
    // Log some useful info
    console.log('Server configuration:', {
      root: server.config.root,
      base: server.config.base,
      publicDir: server.config.publicDir,
      cacheDir: server.config.cacheDir,
    });
  } catch (error) {
    // Log error if server fails to start or any other error occurs
    console.error('Error starting Vite server:', error);
    process.exit(1);
  }
}

main();
