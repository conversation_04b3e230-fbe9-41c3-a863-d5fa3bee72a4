import { useState, useEffect } from 'react';
import { ArrowLeft, Search, User, Building } from 'lucide-react';
import { bitbucketApi } from '../services/api';
import Avatar from '../components/Avatar';

const UserSearchPage = ({ onBack }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [workspaces, setWorkspaces] = useState([]);
  const [workspaceData, setWorkspaceData] = useState({});
  
  // Lae kõik tööruumid lehele tulles
  useEffect(() => {
    const loadAllWorkspaces = async () => {
      try {
        setLoading(true);
        const response = await bitbucketApi.getUserWorkspaces();
        setWorkspaces(response.values || []);
      } catch (error) {
        console.error('Viga tööruumide laadimisel:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadAllWorkspaces();
  }, []);
  
  const searchUser = async () => {
    if (!searchTerm || searchTerm.length < 2) return;
    
    setLoading(true);
    setResults([]);
    
    try {
      // Otsingutulemuste koondaja
      const userResults = {};
      
      // Lae iga tööruumi jaoks õigused
      for (const workspace of workspaces) {
        // Kontrolli, kas andmed on juba olemas
        if (!workspaceData[workspace.slug]) {
          try {
            const overview = await bitbucketApi.getWorkspaceOverview(workspace.slug);
            // Salvesta andmed, et vältida topeltpäringuid
            setWorkspaceData(prev => ({
              ...prev,
              [workspace.slug]: overview
            }));
            
            // Otsi kasutajat
            if (overview && overview.permissions) {
              findUserInPermissions(overview.permissions, workspace, userResults);
            }
          } catch (err) {
            console.error(`Viga tööruumi ${workspace.slug} andmete laadimisel:`, err);
          }
        } else {
          // Kui andmed on juba olemas, kasuta neid
          const overview = workspaceData[workspace.slug];
          if (overview && overview.permissions) {
            findUserInPermissions(overview.permissions, workspace, userResults);
          }
        }
      }
      
      // Teisenda tulemused massiiviks UI jaoks
      setResults(Object.values(userResults));
    } catch (error) {
      console.error('Viga kasutaja otsimisel:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Funktsioon kasutaja leidmiseks õiguste listist
  const findUserInPermissions = (permissions, workspace, results) => {
    const searchTermLower = searchTerm.toLowerCase();
    
    const matchingPermissions = permissions.filter(
      permission => {
        const user = permission.user;
        return user && (
          (user.display_name && user.display_name.toLowerCase().includes(searchTermLower)) ||
          (user.nickname && user.nickname.toLowerCase().includes(searchTermLower)) ||
          (user.username && user.username.toLowerCase().includes(searchTermLower))
        );
      }
    );
    
    // Grupeeri kasutajate kaupa
    matchingPermissions.forEach(permission => {
      const userId = permission.user?.uuid || permission.user?.account_id;
      if (!userId) return;
      
      if (!results[userId]) {
        results[userId] = {
          user: permission.user,
          workspaces: {}
        };
      }
      
      if (!results[userId].workspaces[workspace.slug]) {
        results[userId].workspaces[workspace.slug] = {
          name: workspace.name,
          slug: workspace.slug,
          permissions: []
        };
      }
      
      results[userId].workspaces[workspace.slug].permissions.push({
        repository: permission.repository,
        permission: permission.permission
      });
    });
  };
  
  // Õiguse märgi värvide määramine
  const getPermissionBadgeColor = (permission) => {
    switch (permission) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'write':
        return 'bg-yellow-100 text-yellow-800';
      case 'read':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Funktsioon õiguse eemaldamiseks
  const handleRemovePermission = async (workspaceSlug, repoSlug, userId) => {
    try {
      setLoading(true);
      await bitbucketApi.removeUserFromRepository(workspaceSlug, repoSlug, userId);
      
      // Värskenda tulemusi
      searchUser();
      
      // Värskenda ka workspaceData
      const updatedOverview = await bitbucketApi.getWorkspaceOverview(workspaceSlug);
      setWorkspaceData(prev => ({
        ...prev,
        [workspaceSlug]: updatedOverview
      }));
      
    } catch (error) {
      console.error('Viga õiguse eemaldamisel:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
      <button
        onClick={onBack}
        className="mb-6 inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
      >
        <ArrowLeft className="w-4 h-4" />
        Tagasi avalehele
      </button>
      
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Kasutajad tööruumides</h1>
      
      <div className="mb-8">
        <div className="relative max-w-3xl mx-auto">
          <div className="relative">
            <input
              type="text"
              placeholder="Otsi kasutajaid..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-6 py-4 text-lg border border-gray-200 rounded-full pl-14 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
            />
            <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
            
            <button
              onClick={searchUser}
              disabled={loading || searchTerm.length < 2}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Otsin...' : 'Otsi'}
            </button>
          </div>
          
          <div className="mt-3 text-center text-sm text-gray-500">
            <p>Sisesta vähemalt 2 tähte, et otsida kasutajat kõigist saadaolevatest tööruumidest</p>
          </div>
        </div>
      </div>
      
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-6"></div>
          <p className="text-gray-600 text-lg">Otsin kasutajaid tööruumidest...</p>
        </div>
      )}
      
      {!loading && results.length === 0 && searchTerm.length >= 2 && (
        <div className="max-w-3xl mx-auto text-center py-12 bg-white rounded-lg shadow-sm p-8">
          <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Kasutajat ei leitud</h3>
          <p className="text-gray-600">
            Ühtegi kasutajat nimega "{searchTerm}" ei leitud saadaolevatest tööruumidest.
          </p>
        </div>
      )}
      
      {!loading && results.length > 0 && (
        <div className="space-y-6 max-w-5xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-semibold text-gray-900">Otsingu tulemused</h2>
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              {results.length} kasutajat leitud
            </span>
          </div>
          
          {results.map((result) => (
            <div key={result.user.uuid || result.user.account_id} className="bg-white rounded-lg shadow-sm p-6 mb-4 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4 mb-4">
                <Avatar
                  src={result.user?.links?.avatar?.href}
                  alt={`${result.user?.display_name || result.user?.nickname} avatar`}
                  type="user"
                  size="lg"
                  fallbackName={result.user?.display_name || result.user?.nickname}
                  className="rounded-full"
                />
                <div>
                  <h3 className="text-xl font-medium text-gray-900">
                    {result.user?.display_name || result.user?.nickname || 'Tundmatu kasutaja'}
                  </h3>
                  <p className="text-sm text-gray-600">@{result.user?.nickname || result.user?.username || 'tundmatu'}</p>
                  <div className="mt-2 flex items-center gap-2">
                    <span className="px-2 py-1 bg-blue-50 text-blue-700 text-xs font-medium rounded-full">
                      {Object.keys(result.workspaces).length} tööruumi
                    </span>
                    <span className="px-2 py-1 bg-green-50 text-green-700 text-xs font-medium rounded-full">
                      {Object.values(result.workspaces).reduce((total, ws) => total + ws.permissions.length, 0)} repositooriumi
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4 mt-4">
                {Object.values(result.workspaces).map((workspace) => (
                  <div key={workspace.slug} className="border border-gray-200 rounded-lg p-5 bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Building className="w-5 h-5 text-blue-600" />
                        <h4 className="font-medium text-gray-900 text-lg">{workspace.name}</h4>
                      </div>
                      <span className="px-2.5 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                        {workspace.permissions.length} repositooriumi
                      </span>
                    </div>
                    
                    <div className="grid gap-2">
                      {workspace.permissions.map((perm, i) => (
                        <div 
                          key={`${workspace.slug}-${perm.repository.name}-${i}`}
                          className="flex items-center justify-between py-3 px-4 bg-white rounded-md shadow-sm border border-gray-100"
                        >
                          <div className="flex items-center gap-3">
                            <span className="w-1.5 h-8 rounded-full bg-blue-500"></span>
                            <div>
                              <span className="font-medium text-gray-800">{perm.repository.name}</span>
                              <div className="mt-1">
                                <span className={`px-2.5 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(perm.permission)}`}>
                                  {perm.permission}
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          <button
                            onClick={() => handleRemovePermission(
                              workspace.slug,
                              perm.repository.name,
                              result.user.uuid || result.user.account_id
                            )}
                            className="px-3 py-1.5 text-xs font-medium text-red-600 hover:text-red-800 border border-red-200 rounded-md hover:bg-red-50"
                          >
                            Eemalda õigus
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UserSearchPage;
