import { useState, useEffect } from 'react';
import Avatar from '../components/Avatar';
import { avatarService } from '../services/avatarService';

const BitbucketAvatarsTestPage = () => {
  const [testUsers] = useState([
    { name: '<PERSON><PERSON><PERSON>', id: '111e0832-7787-4e38-ab90-f8775b1673eb' },
    { name: '<PERSON>', id: 'c585389b-93bf-4e17-9d1b-448ca389c6a9' },
    { name: '<PERSON><PERSON><PERSON>', id: '0600359a-6ddd-4193-b94b-4baf8ae04eec' }
  ]);
  
  const [avatarUrls, setAvatarUrls] = useState({
    workspaces: {},
    users: {},
    fallbacks: {}
  });
  
  // Tööruumide avatarid
  const testWorkspaces = ['ovaal', 'hennot', 'testcompany'];
  
  useEffect(() => {
    // Hangi tööruumide avatarid
    const workspaceAvatars = {};
    testWorkspaces.forEach(workspace => {
      workspaceAvatars[workspace] = avatarService.getWorkspaceAvatarUrl(workspace);
    });
    
    // Hangi kasutajate avatarid
    async function fetchUserAvatars() {
      const userAvatars = {};
      const fallbackAvatars = {};
      
      for (const user of testUsers) {
        try {
          // Päris avatar Bitbucketist
          const avatarUrl = await avatarService.getUserAvatarUrl(user.id);
          userAvatars[user.id] = avatarUrl;
          
          // Ekstrakti Atlassiani tagavara avatar
          if (avatarUrl) {
            fallbackAvatars[user.id] = avatarService.extractAtlassianAvatarUrl(avatarUrl);
          }
        } catch (error) {
          console.error(`Viga kasutaja ${user.name} avatari hankimisel:`, error);
        }
      }
      
      setAvatarUrls(prev => ({
        ...prev,
        users: userAvatars,
        fallbacks: fallbackAvatars
      }));
    }
    
    setAvatarUrls(prev => ({
      ...prev,
      workspaces: workspaceAvatars
    }));
    
    fetchUserAvatars();
  }, []);
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Bitbucketi Avataride Test</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Tööruumide Avatarid</h2>
        <div className="flex flex-wrap gap-6">
          {testWorkspaces.map(workspace => (
            <div key={workspace} className="flex flex-col items-center p-4 bg-white rounded-lg shadow">
              <Avatar
                src={avatarUrls.workspaces[workspace]}
                alt={`${workspace} workspace`}
                fallbackName={workspace}
                type="workspace"
                size="lg"
              />
              <p className="mt-2 font-medium">{workspace}</p>
              <p className="text-xs text-gray-500 max-w-xs truncate">{avatarUrls.workspaces[workspace]}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Kasutajate Avatarid</h2>
        <div className="flex flex-wrap gap-6">
          {testUsers.map(user => (
            <div key={user.id} className="flex flex-col items-center p-4 bg-white rounded-lg shadow">
              <Avatar
                src={avatarUrls.users[user.id]}
                alt={`${user.name} avatar`}
                fallbackName={user.name}
                size="lg"
              />
              <p className="mt-2 font-medium">{user.name}</p>
              <p className="text-xs text-gray-500 max-w-xs truncate">{avatarUrls.users[user.id]}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Atlassiani Tagavara Avatarid</h2>
        <div className="flex flex-wrap gap-6">
          {testUsers.map(user => (
            <div key={user.id} className="flex flex-col items-center p-4 bg-white rounded-lg shadow">
              <Avatar
                src={avatarUrls.fallbacks[user.id]}
                alt={`${user.name} fallback avatar`}
                fallbackName={user.name}
                size="lg"
              />
              <p className="mt-2 font-medium">{user.name} (Atlassian)</p>
              <p className="text-xs text-gray-500 max-w-xs truncate">{avatarUrls.fallbacks[user.id]}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Genereeritud UI Avatarid</h2>
        <div className="flex flex-wrap gap-6">
          {testUsers.map(user => (
            <div key={user.id} className="flex flex-col items-center p-4 bg-white rounded-lg shadow">
              <img 
                src={avatarService.generateFallbackAvatarUrl(user.name)}
                alt={`${user.name} UI avatar`}
                className="w-16 h-16 rounded-full"
              />
              <p className="mt-2 font-medium">{user.name} (UI Avatars)</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BitbucketAvatarsTestPage;
