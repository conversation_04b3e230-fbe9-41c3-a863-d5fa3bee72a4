import { useState, useEffect } from 'react';
import { ArrowLeft, Search, GitBranch, Users, Shield, Check, X, Edit2, Trash2, <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';
import { bitbucketApi } from '../services/api';
import Avatar from '../components/Avatar';

const AdminRepositoriesPage = ({ onBack }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [workspaces, setWorkspaces] = useState([]);
  const [adminRepositories, setAdminRepositories] = useState([]);
  const [selectedRepository, setSelectedRepository] = useState(null);
  const [repositoryUsers, setRepositoryUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingUser, setEditingUser] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState(null);

  // Load workspaces and admin repositories on mount
  useEffect(() => {
    loadWorkspacesAndAdminRepos();
  }, []);

  const loadWorkspacesAndAdminRepos = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get all workspaces
      const workspacesResponse = await bitbucketApi.getUserWorkspaces();
      const allWorkspaces = workspacesResponse.values || [];
      setWorkspaces(allWorkspaces);
      
      // For each workspace, get repositories and filter for admin access
      const adminRepos = [];
      
      for (const workspace of allWorkspaces) {
        // Get repositories in the workspace
        const repositoriesResponse = await bitbucketApi.getWorkspaceRepositories(workspace.slug);
        const repositories = repositoriesResponse.values || [];
        
        // Get permissions for workspace repositories to find admin repos
        const permissionsResponse = await bitbucketApi.getWorkspacePermissions(workspace.slug);
        const permissions = permissionsResponse?.values || [];
        
        // Find repositories where the user is an admin
        for (const repo of repositories) {
          const userPermissions = permissions.filter(
            p => p.repository?.uuid === repo.uuid || p.repository?.name === repo.name
          );
          
          const isAdmin = userPermissions.some(p => p.permission === 'admin');
          
          if (isAdmin) {
            adminRepos.push({
              ...repo,
              workspace: workspace,
              permissions: userPermissions.filter(p => p.repository?.uuid === repo.uuid)
            });
          }
        }
      }
      
      setAdminRepositories(adminRepos);
    } catch (err) {
      console.error("Error loading admin repositories:", err);
      setError(err.message || "Viga administraatori repositooriumite laadimisel");
    } finally {
      setLoading(false);
    }
  };
  
  const loadRepositoryUsers = async (workspace, repoSlug) => {
    setLoading(true);
    try {
      const response = await bitbucketApi.getRepositoryUsers(workspace, repoSlug);
      setRepositoryUsers(response.values || []);
    } catch (err) {
      console.error("Error loading repository users:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRepositorySelect = async (repository) => {
    setSelectedRepository(repository);
    await loadRepositoryUsers(repository.workspace.slug, repository.slug);
  };
  
  const handleBackToRepositories = () => {
    setSelectedRepository(null);
    setRepositoryUsers([]);
    setEditingUser(null);
  };
  
  const handleRemoveUser = async (userId) => {
    if (!selectedRepository) return;
    
    try {
      setLoading(true);
      await bitbucketApi.removeUserFromRepository(
        selectedRepository.workspace.slug,
        selectedRepository.slug,
        userId
      );
      
      // Reload the repository users
      await loadRepositoryUsers(selectedRepository.workspace.slug, selectedRepository.slug);
      
    } catch (err) {
      console.error("Error removing user:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleChangePermission = async (userId, newPermission) => {
    if (!selectedRepository) return;
    
    try {
      setLoading(true);
      await bitbucketApi.updateUserRepositoryPermission(
        selectedRepository.workspace.slug,
        selectedRepository.slug,
        userId,
        newPermission
      );
      
      // Reload the repository users
      await loadRepositoryUsers(selectedRepository.workspace.slug, selectedRepository.slug);
      setEditingUser(null);
      
    } catch (err) {
      console.error("Error updating permission:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRemoveConfirm = (user) => {
    setConfirmDialog({
      title: "Kinnita kasutaja õiguse eemaldamine",
      message: `Kas oled kindel, et soovid eemaldada kasutaja ${user.display_name} õigused repositooriumist ${selectedRepository.name}?`,
      confirmLabel: "Eemalda õigus",
      cancelLabel: "Tühista",
      onConfirm: () => {
        handleRemoveUser(user.uuid || user.account_id);
        setConfirmDialog(null);
      },
      onCancel: () => setConfirmDialog(null)
    });
  };
  
  // Filter repositories based on search term
  const filteredRepositories = adminRepositories.filter(repo => {
    return searchTerm === '' || 
      repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repo.workspace.name.toLowerCase().includes(searchTerm.toLowerCase());
  });
  
  // Filter repository users based on search term
  const filteredUsers = repositoryUsers.filter(user => {
    return searchTerm === '' || 
      (user.user?.display_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.user?.nickname || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.user?.account_id || '').toLowerCase().includes(searchTerm.toLowerCase());
  });
  
  // Helper to determine badge color for permission levels
  const getPermissionBadgeColor = (permission) => {
    switch (permission) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'write': return 'bg-yellow-100 text-yellow-800';
      case 'read': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 bg-gray-50 min-h-screen">
      {/* Back button */}
      <button
        onClick={onBack}
        className="mb-6 inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
      >
        <ArrowLeft className="w-4 h-4" />
        Tagasi avalehele
      </button>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center gap-2 text-red-700">
            <AlertTriangle className="w-5 h-5" />
            <p className="font-medium">{error}</p>
          </div>
          <button 
            onClick={loadWorkspacesAndAdminRepos}
            className="mt-2 px-3 py-1 text-sm text-red-700 border border-red-300 rounded-md hover:bg-red-100"
          >
            Proovi uuesti
          </button>
        </div>
      )}
      
      {loading && !selectedRepository && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-6"></div>
          <p className="text-gray-600 text-lg">Laadin administraatori repositooriumeid...</p>
        </div>
      )}
      
      {!loading && !selectedRepository && (
        <>
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Administraatori repositooriumid</h1>
          
          {/* Search bar */}
          <div className="mb-8">
            <div className="relative max-w-3xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Otsi repositooriumeid..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-6 py-4 text-lg border border-gray-200 rounded-full pl-14 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                />
                <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
              </div>
            </div>
          </div>
          
          {/* Repositories list */}
          {filteredRepositories.length === 0 ? (
            <div className="max-w-3xl mx-auto text-center py-12 bg-white rounded-lg shadow-sm p-8">
              <GitBranch className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">Repositooriumeid ei leitud</h3>
              <p className="text-gray-600">
                {searchTerm ? 
                  `Ühtegi repositooriumi nimega "${searchTerm}" ei leitud, kus sul oleks administraatori õigused.` :
                  'Sul ei ole üheski repositooriumis administraatori õiguseid.'}
              </p>
            </div>
          ) : (
            <div className="space-y-6 max-w-5xl mx-auto">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-semibold text-gray-900">Repositooriumid</h2>
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {filteredRepositories.length} repositooriumi
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredRepositories.map((repo) => (
                  <div 
                    key={repo.uuid} 
                    className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleRepositorySelect(repo)}
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-blue-50 rounded-full">
                        <GitBranch className="w-6 h-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{repo.name}</h3>
                        <p className="text-sm text-gray-600">{repo.workspace.name}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
                      <span className="text-sm text-gray-500">{repo.permissions?.length || 0} kasutajat</span>
                      <span className="px-2 py-1 bg-red-50 text-red-700 text-xs font-medium rounded-full">
                        Administraator
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Repository detail view */}
      {selectedRepository && (
        <>
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={handleBackToRepositories}
              className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              <ArrowLeft className="w-4 h-4" />
              Tagasi repositooriumite nimekirja
            </button>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{selectedRepository.name}</h1>
              <p className="text-gray-600">Tööruumis {selectedRepository.workspace.name}</p>
            </div>
            
            <button
              onClick={() => loadRepositoryUsers(selectedRepository.workspace.slug, selectedRepository.slug)}
              className="ml-auto inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Värskenda
            </button>
          </div>
          
          {/* Search bar for users */}
          <div className="mb-8">
            <div className="relative max-w-3xl">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Otsi kasutajaid repositooriumis..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg pl-10 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
            </div>
          </div>
          
          {/* Users list */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Laadin kasutajaid...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="max-w-3xl mx-auto text-center py-8 bg-white rounded-lg shadow-sm p-6">
              <Users className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">Kasutajaid ei leitud</h3>
              <p className="text-gray-600">
                {searchTerm ? 
                  `Ühtegi kasutajat nimega "${searchTerm}" ei leitud selles repositooriumis.` :
                  'Selles repositooriumis pole ühtegi kasutajat.'}
              </p>
            </div>
          ) : (
            <div className="space-y-4 bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Kasutajad repositooriumis</h2>
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {filteredUsers.length} kasutajat
                </span>
              </div>
              
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kasutaja</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Õigus</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tegevused</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((userPermission) => (
                    <tr key={userPermission.user?.uuid || userPermission.user?.account_id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Avatar
                            src={userPermission.user?.links?.avatar?.href}
                            alt={`${userPermission.user?.display_name || userPermission.user?.nickname} avatar`}
                            type="user"
                            size="sm"
                            fallbackName={userPermission.user?.display_name || userPermission.user?.nickname}
                            className="mr-3"
                          />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {userPermission.user?.display_name || userPermission.user?.nickname || 'Tundmatu kasutaja'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {userPermission.user?.nickname ? `@${userPermission.user.nickname}` : ''}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingUser === (userPermission.user?.uuid || userPermission.user?.account_id) ? (
                          <div className="flex items-center gap-2">
                            <select 
                              defaultValue={userPermission.permission}
                              className="text-sm border border-gray-300 rounded px-3 py-1"
                            >
                              <option value="admin">Admin</option>
                              <option value="write">Write</option>
                              <option value="read">Read</option>
                            </select>
                            <button
                              onClick={() => handleChangePermission(
                                userPermission.user?.uuid || userPermission.user?.account_id, 
                                document.querySelector('select').value
                              )}
                              className="p-1 text-blue-600 hover:text-blue-800"
                            >
                              <Check className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setEditingUser(null)}
                              className="p-1 text-gray-600 hover:text-gray-800"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        ) : (
                          <span className={`px-2.5 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(userPermission.permission)}`}>
                            {userPermission.permission}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => setEditingUser(userPermission.user?.uuid || userPermission.user?.account_id)}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                            disabled={editingUser !== null}
                          >
                            <Edit2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleRemoveConfirm(userPermission.user)}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
      
      {/* Confirmation dialog */}
      {confirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">{confirmDialog.title}</h3>
              <button onClick={confirmDialog.onCancel} className="text-gray-400 hover:text-gray-600">
                ×
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-2">
                <p>{confirmDialog.message}</p>
                <div className="flex items-center gap-2 text-amber-600">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm">See tegevus on pöördumatu!</span>
                </div>
              </div>
            </div>

            <div className="flex gap-3 p-6 border-t border-gray-200">
              <button
                onClick={confirmDialog.onCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex-1"
              >
                {confirmDialog.cancelLabel}
              </button>
              <button
                onClick={confirmDialog.onConfirm}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 flex-1"
              >
                {confirmDialog.confirmLabel}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminRepositoriesPage;
