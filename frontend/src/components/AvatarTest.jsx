import React from 'react';
import Avatar from './Avatar';

const AvatarTest = () => {
  // Test URLs from our debug output
  const testAvatars = [
    {
      name: '<PERSON><PERSON><PERSON>',
      url: 'https://secure.gravatar.com/avatar/66b7ec1f3ae9fca282787c589509bf3c?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FHT-3.png'
    },
    {
      name: '<PERSON>', 
      url: 'https://secure.gravatar.com/avatar/5f435eb35ef39205a2d6fc9124ae68f7?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAG-3.png'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      url: 'https://secure.gravatar.com/avatar/cb69f98da8fa63aea33c54c048542f82?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FMK-6.png'
    }
  ];

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-8">Avatar Test</h1>
      
      <div className="space-y-8">
        <h2 className="text-xl font-semibold">Test Real Bitbucket Avatar URLs:</h2>
        <div className="flex flex-wrap gap-4">
          {testAvatars.map((avatar, index) => (
            <div key={index} className="flex flex-col items-center gap-2 p-4 bg-white rounded-lg shadow">
              <Avatar
                src={avatar.url}
                alt={`${avatar.name} avatar`}
                type="user"
                size="lg"
                fallbackName={avatar.name}
              />
              <span className="text-sm text-center font-medium">{avatar.name}</span>
              <span className="text-xs text-gray-500 text-center break-all max-w-48">{avatar.url}</span>
            </div>
          ))}
        </div>

        <h2 className="text-xl font-semibold">Test Fallback (no src):</h2>
        <div className="flex flex-wrap gap-4">
          {testAvatars.map((avatar, index) => (
            <div key={index} className="flex flex-col items-center gap-2 p-4 bg-white rounded-lg shadow">
              <Avatar
                src={null}
                alt={`${avatar.name} avatar`}
                type="user"
                size="lg"
                fallbackName={avatar.name}
              />
              <span className="text-sm text-center font-medium">{avatar.name} (fallback)</span>
            </div>
          ))}
        </div>

        <h2 className="text-xl font-semibold">Test Invalid URL:</h2>
        <div className="flex flex-wrap gap-4">
          <div className="flex flex-col items-center gap-2 p-4 bg-white rounded-lg shadow">
            <Avatar
              src="https://invalid-url-that-should-fail.com/avatar.png"
              alt="Invalid avatar"
              type="user"
              size="lg"
              fallbackName="Test User"
            />
            <span className="text-sm text-center font-medium">Invalid URL</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarTest;
