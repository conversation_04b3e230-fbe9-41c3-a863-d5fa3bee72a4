import { useState } from 'react';
import { Search } from 'lucide-react';

const WorkspaceSelector = ({ onWorkspaceSelect, loading }) => {
  const [workspace, setWorkspace] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (workspace.trim()) {
      onWorkspaceSelect(workspace.trim());
    }
  };

  return (
    <div className="card max-w-md mx-auto">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Bitbucket Ligipääsude Haldus
        </h1>
        <p className="text-gray-600">
          Sisesta workspace nimi, et vaadata kasutajate õigusi
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="workspace" className="block text-sm font-medium text-gray-700 mb-2">
            Workspace nimi
          </label>
          <div className="relative">
            <input
              type="text"
              id="workspace"
              value={workspace}
              onChange={(e) => setWorkspace(e.target.value)}
              placeholder="nt. minu-ettevõte"
              className="input pl-10"
              disabled={loading}
              required
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          </div>
        </div>

        <button
          type="submit"
          disabled={loading || !workspace.trim()}
          className="btn btn-primary w-full"
        >
          {loading ? 'Laadin...' : 'Vaata õigusi'}
        </button>
      </form>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          <strong>Hoiatus:</strong> See tööriist võimaldab eemaldada kasutajate ligipääsu repositooriumidele. 
          Kasuta ettevaatlikult!
        </p>
      </div>
    </div>
  );
};

export default WorkspaceSelector;
