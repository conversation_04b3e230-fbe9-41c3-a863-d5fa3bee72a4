import { useState, useEffect } from 'react';
import { Clock, User, GitBranch, Shield, AlertTriangle, CheckCircle, Eye } from 'lucide-react';
import { bitbucketApi } from '../services/api';

const AuditLog = ({ workspace }) => {
  const [auditLog, setAuditLog] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [limit, setLimit] = useState(50);

  useEffect(() => {
    if (workspace) {
      loadAuditLog();
    }
  }, [workspace, limit]);

  const loadAuditLog = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await bitbucketApi.getAuditLog(workspace, limit);
      setAuditLog(data.entries || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (actionType) => {
    switch (actionType) {
      case 'permission_removed':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'permission_granted':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'workspace_checked':
        return <Eye className="w-4 h-4 text-blue-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActionColor = (actionType) => {
    switch (actionType) {
      case 'permission_removed':
        return 'bg-red-50 border-red-200';
      case 'permission_granted':
        return 'bg-green-50 border-green-200';
      case 'workspace_checked':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getActionText = (actionType) => {
    switch (actionType) {
      case 'permission_removed':
        return 'Õigus eemaldatud';
      case 'permission_granted':
        return 'Õigus antud';
      case 'workspace_checked':
        return 'Workspace kontrollitud';
      default:
        return actionType;
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString('et-EE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getPermissionBadgeColor = (permission) => {
    switch (permission) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'write':
        return 'bg-yellow-100 text-yellow-800';
      case 'read':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!workspace) {
    return null;
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Clock className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">Toimingute ajalugu</h3>
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={limit}
            onChange={(e) => setLimit(parseInt(e.target.value))}
            className="text-sm border border-gray-300 rounded px-3 py-1"
          >
            <option value={25}>25 kirjet</option>
            <option value={50}>50 kirjet</option>
            <option value={100}>100 kirjet</option>
            <option value={200}>200 kirjet</option>
          </select>
          
          <button
            onClick={loadAuditLog}
            disabled={loading}
            className="btn btn-secondary text-sm"
          >
            {loading ? 'Laadin...' : 'Värskenda'}
          </button>
        </div>
      </div>

      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Laadin ajalugu...</p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {!loading && !error && auditLog.length === 0 && (
        <div className="text-center py-8">
          <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">Ajalugu puudub</h4>
          <p className="text-gray-600">Selles workspace'is pole veel toiminguid tehtud.</p>
        </div>
      )}

      {!loading && !error && auditLog.length > 0 && (
        <div className="space-y-3">
          {auditLog.map((entry) => (
            <div
              key={entry.id}
              className={`border rounded-lg p-4 ${getActionColor(entry.action_type)}`}
            >
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  {getActionIcon(entry.action_type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium text-gray-900">
                      {getActionText(entry.action_type)}
                    </span>
                    {entry.permission_type && (
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(entry.permission_type)}`}>
                        {entry.permission_type}
                      </span>
                    )}
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-700">
                    {entry.user_name && (
                      <div className="flex items-center gap-2">
                        <User className="w-3 h-3" />
                        <span>{entry.user_name} (@{entry.user_nickname})</span>
                      </div>
                    )}
                    
                    {entry.repository_name && (
                      <div className="flex items-center gap-2">
                        <GitBranch className="w-3 h-3" />
                        <span>{entry.repository_name}</span>
                      </div>
                    )}
                    
                    {entry.performed_by && (
                      <div className="flex items-center gap-2">
                        <Shield className="w-3 h-3" />
                        <span>Tegi: {entry.performed_by}</span>
                      </div>
                    )}
                  </div>
                  
                  {entry.details && (
                    <div className="mt-2 text-xs text-gray-600 bg-white bg-opacity-50 rounded p-2">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(JSON.parse(entry.details), null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
                
                <div className="text-xs text-gray-500 whitespace-nowrap">
                  {formatTimestamp(entry.timestamp)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AuditLog;
