import { Users, GitBranch, Shield, Building } from 'lucide-react';

const WorkspaceStats = ({ workspaceData }) => {
  if (!workspaceData) return null;

  const stats = [
    {
      label: 'Liikmed',
      value: workspaceData.totalMembers || 0,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      label: 'Repositooriumid',
      value: workspaceData.totalRepositories || 0,
      icon: GitBranch,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      label: 'Õigused',
      value: workspaceData.totalPermissions || 0,
      icon: Shield,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      label: 'Projektid',
      value: workspaceData.projects?.length || 0,
      icon: Building,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <div key={stat.label} className="card text-center">
            <div className={`w-12 h-12 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-3`}>
              <Icon className={`w-6 h-6 ${stat.color}`} />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {stat.value}
            </div>
            <div className="text-sm text-gray-600">
              {stat.label}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default WorkspaceStats;
