import { useState, useMemo } from 'react';
import { Trash2, Search, Filter, AlertTriangle, User, GitBranch } from 'lucide-react';
import ConfirmDialog from './ConfirmDialog';

const PermissionsList = ({ permissions, repositories, onRemovePermission, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [permissionFilter, setPermissionFilter] = useState('all');
  const [confirmDialog, setConfirmDialog] = useState(null);

  // Filtreeri ja otsi õigusi
  const filteredPermissions = useMemo(() => {
    if (!permissions || !permissions.length) return [];

    return permissions.filter(permission => {
      const user = permission.user;
      const repo = permission.repository;
      
      // Otsing kasutaja nime või repo nime järgi
      const matchesSearch = searchTerm === '' || 
        user.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.nickname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.name?.toLowerCase().includes(searchTerm.toLowerCase());

      // Filtreeri õiguste tüübi järgi
      const matchesPermission = permissionFilter === 'all' || 
        permission.permission === permissionFilter;

      return matchesSearch && matchesPermission;
    });
  }, [permissions, searchTerm, permissionFilter]);

  // Grupeeri kasutajate kaupa
  const groupedByUser = useMemo(() => {
    const groups = {};
    filteredPermissions.forEach(permission => {
      const userId = permission.user.uuid;
      if (!groups[userId]) {
        groups[userId] = {
          user: permission.user,
          permissions: []
        };
      }
      groups[userId].permissions.push(permission);
    });
    return Object.values(groups);
  }, [filteredPermissions]);

  const handleRemoveClick = (permission) => {
    setConfirmDialog({
      user: permission.user,
      repository: permission.repository,
      permission: permission.permission,
      onConfirm: () => {
        onRemovePermission(permission.repository.name, permission.user.account_id || permission.user.uuid);
        setConfirmDialog(null);
      },
      onCancel: () => setConfirmDialog(null)
    });
  };

  const getPermissionBadgeColor = (permission) => {
    switch (permission) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'write':
        return 'bg-yellow-100 text-yellow-800';
      case 'read':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!permissions || permissions.length === 0) {
    return (
      <div className="card text-center">
        <GitBranch className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Õigusi ei leitud</h3>
        <p className="text-gray-600">Selles workspace'is ei ole ühtegi kasutajat repositooriumi õigustega.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filtrid ja otsing */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Otsi kasutajaid või repositooriume..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
          </div>
          
          <div className="sm:w-48">
            <select
              value={permissionFilter}
              onChange={(e) => setPermissionFilter(e.target.value)}
              className="input"
            >
              <option value="all">Kõik õigused</option>
              <option value="admin">Admin</option>
              <option value="write">Write</option>
              <option value="read">Read</option>
            </select>
          </div>
        </div>

        <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
          <span>Kokku: {filteredPermissions.length} õigust</span>
          <span>Kasutajaid: {groupedByUser.length}</span>
        </div>
      </div>

      {/* Kasutajate nimekiri */}
      <div className="space-y-4">
        {groupedByUser.map(({ user, permissions: userPermissions }) => (
          <div key={user.uuid} className="card">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-primary-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{user.display_name}</h3>
                  <p className="text-sm text-gray-600">@{user.nickname}</p>
                </div>
              </div>
              <span className="text-sm text-gray-500">
                {userPermissions.length} repositooriumi
              </span>
            </div>

            <div className="space-y-2">
              {userPermissions.map((permission) => (
                <div
                  key={`${permission.repository.uuid}-${user.uuid}`}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <GitBranch className="w-4 h-4 text-gray-400" />
                    <div>
                      <span className="font-medium text-gray-900">
                        {permission.repository.name}
                      </span>
                      <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(permission.permission)}`}>
                        {permission.permission}
                      </span>
                    </div>
                  </div>

                  <button
                    onClick={() => handleRemoveClick(permission)}
                    disabled={loading}
                    className="btn btn-danger text-sm px-3 py-1"
                    title="Eemalda õigus"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Kinnituse dialoog */}
      {confirmDialog && (
        <ConfirmDialog
          title="Eemalda kasutaja õigus"
          message={
            <div className="space-y-2">
              <p>Kas oled kindel, et soovid eemaldada järgmise õiguse?</p>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p><strong>Kasutaja:</strong> {confirmDialog.user.display_name}</p>
                <p><strong>Repositoorium:</strong> {confirmDialog.repository.name}</p>
                <p><strong>Õigus:</strong> {confirmDialog.permission}</p>
              </div>
              <div className="flex items-center gap-2 text-amber-600">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-sm">See tegevus on pöördumatu!</span>
              </div>
            </div>
          }
          onConfirm={confirmDialog.onConfirm}
          onCancel={confirmDialog.onCancel}
          confirmText="Eemalda õigus"
          confirmButtonClass="btn-danger"
        />
      )}
    </div>
  );
};

export default PermissionsList;
