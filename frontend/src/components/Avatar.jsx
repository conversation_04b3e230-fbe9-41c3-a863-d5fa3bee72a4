import { useState, useEffect } from 'react';
import { User, Building } from 'lucide-react';

const Avatar = ({
  src,
  alt,
  size = 'md',
  type = 'user', // 'user' or 'workspace'
  className = '',
  fallbackName = ''
}) => {
  const [primarySrcError, setPrimarySrcError] = useState(false);
  const [secondarySrcError, setSecondarySrcError] = useState(false);
  const [tertiarySrcError, setTertiarySrcError] = useState(false);

  // Reset errors when src changes
  useEffect(() => {
    setPrimarySrcError(false);
    setSecondarySrcError(false);
    setTertiarySrcError(false);
  }, [src]);

  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8', 
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };
  
  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5', 
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };
  
  const isRounded = type === 'user' ? 'rounded-full' : 'rounded-lg';
  const Icon = type === 'user' ? User : Building;
  
  // Generate initials from name
  const getInitials = (name) => {
    if (!name) return '';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };
  
  const initials = getInitials(fallbackName);

  // Clean and validate the src URL
  const cleanSrc = src ? src.trim() : null;
  const isValidUrl = cleanSrc && (cleanSrc.startsWith('http://') || cleanSrc.startsWith('https://'));

  // For Gravatar URLs with fallbacks, try to extract and use the fallback directly
  const extractAtlassianFallback = (gravatarUrl) => {
    if (!gravatarUrl || !gravatarUrl.includes('gravatar.com')) return null;
    
    try {
      const url = new URL(gravatarUrl);
      const fallbackParam = url.searchParams.get('d');
      if (fallbackParam && fallbackParam.includes('avatar-management--avatars')) {
        return decodeURIComponent(fallbackParam);
      }
    } catch (e) {
      console.log('Failed to extract Atlassian fallback:', e);
    }
    return null;
  };

  // Generate alternative avatar URL if original fails
  const generateFallbackAvatar = (name, type) => {
    if (!name) return null;

    // Use UI Avatars service for consistent avatars (no CORS issues)
    const cleanName = name.replace(/[^a-zA-Z0-9\s]/g, '').trim();

    // Different colors for different types
    const colors = {
      workspace: { bg: '6366f1', text: 'ffffff' }, // Indigo
      user: { bg: '3b82f6', text: 'ffffff' }       // Blue
    };

    const colorScheme = colors[type] || colors.user;

    return `https://ui-avatars.com/api/?name=${encodeURIComponent(cleanName)}&background=${colorScheme.bg}&color=${colorScheme.text}&size=80&bold=true&format=svg`;
  };

  const primarySrc = cleanSrc;
  const secondaryFallbackSrc = extractAtlassianFallback(primarySrc);
  const tertiaryFallbackSrc = generateFallbackAvatar(fallbackName, type);

  // Debug logging for troubleshooting
  console.log(`Avatar debug for "${fallbackName}" (type: ${type}):`, {
    originalSrc: src,
    primarySrc,
    secondaryFallbackSrc,
    tertiaryFallbackSrc,
    isValidUrl,
    primarySrcError,
    secondarySrcError,
    tertiarySrcError,
    initialsGenerated: initials
  });

  // Render logic with improved fallback chain
  if (isValidUrl && !primarySrcError) {
    return (
      <div className={`${sizeClasses[size]} ${isRounded} overflow-hidden flex-shrink-0 ${className} transition-all duration-200 hover:shadow-md`}>
        <img
          src={primarySrc}
          alt={alt}
          className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          onLoad={() => {
            console.log(`✅ Primary src loaded: ${fallbackName} (${primarySrc})`);
          }}
          onError={() => {
            console.log(`❌ Primary src failed: ${fallbackName} (${primarySrc})`);
            setPrimarySrcError(true);
          }}
        />
      </div>
    );
  }

  if (secondaryFallbackSrc && !secondarySrcError) {
    return (
      <div className={`${sizeClasses[size]} ${isRounded} overflow-hidden flex-shrink-0 ${className} transition-all duration-200 hover:shadow-md`}>
        <img
          src={secondaryFallbackSrc}
          alt={alt}
          className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          onLoad={() => {
            console.log(`✅ Secondary fallback loaded: ${fallbackName} (${secondaryFallbackSrc})`);
          }}
          onError={() => {
            console.log(`❌ Secondary fallback failed: ${fallbackName} (${secondaryFallbackSrc})`);
            setSecondarySrcError(true);
          }}
        />
      </div>
    );
  }

  if (tertiaryFallbackSrc && !tertiarySrcError) {
    return (
      <div className={`${sizeClasses[size]} ${isRounded} overflow-hidden flex-shrink-0 ${className} transition-all duration-200 hover:shadow-md`}>
        <img
          src={tertiaryFallbackSrc}
          alt={alt}
          className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          onLoad={() => {
            console.log(`✅ Tertiary fallback (UI Avatars) loaded: ${fallbackName} (${tertiaryFallbackSrc})`);
          }}
          onError={() => {
            console.log(`❌ Tertiary fallback (UI Avatars) failed: ${fallbackName} (${tertiaryFallbackSrc})`);
            setTertiarySrcError(true);
          }}
        />
      </div>
    );
  }

  // Final fallback: Initials or Icon
  console.log(`🎨 Rendering final fallback for "${fallbackName}": initials="${initials}" (Is initials truthy? ${!!initials})`);
  return (
    <div className={`${sizeClasses[size]} ${isRounded} overflow-hidden flex-shrink-0 ${className} transition-all duration-200 hover:shadow-md flex items-center justify-center ${type === 'workspace' ? 'bg-gradient-to-br from-indigo-400 to-indigo-600' : 'bg-gradient-to-br from-blue-400 to-blue-600'}`}>
      {initials ? (
        <span className={`font-bold text-white ${
          size === 'xs' ? 'text-xs' :
          size === 'sm' ? 'text-xs' :
          size === 'md' ? 'text-sm' :
          size === 'lg' ? 'text-base' :
          'text-lg'
        }`}>
          {initials}
        </span>
      ) : (
        <Icon className={`${iconSizes[size]} text-white`} />
      )}
    </div>
  );
};

export default Avatar;
