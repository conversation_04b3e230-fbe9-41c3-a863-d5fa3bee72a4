import { useState, useEffect } from 'react';

// Extremely simple app for testing
function AppTest() {
  const [message, setMessage] = useState('Initial render');

  useEffect(() => {
    console.log('AppTest component mounted');
    setMessage('Component mounted successfully');
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Test Application</h1>
      <div style={{ padding: '15px', backgroundColor: '#f0f0f0', borderRadius: '5px', marginTop: '20px' }}>
        <h2>Status</h2>
        <p>{message}</p>
        <button 
          onClick={() => setMessage('Button clicked: ' + new Date().toLocaleTimeString())}
          style={{ 
            padding: '8px 16px',
            backgroundColor: '#0066cc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Click Me
        </button>
      </div>
    </div>
  );
}

export default AppTest;
