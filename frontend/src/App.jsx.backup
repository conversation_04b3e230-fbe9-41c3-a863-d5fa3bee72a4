import { useState, useEffect } from 'react';
import { ArrowLeft, Settings, Users, GitBranch, Shield, Building, Search, Trash2, AlertTriangle, History, Grid, List, ChevronUp, ChevronDown, RefreshCw, Filter, X } from 'lucide-react';
import { bitbucketApi } from './services/api';
import AuditLog from './components/AuditLog';
import Avatar from './components/Avatar';
import BitbucketAvatarsTestPage from './pages/BitbucketAvatarsTestPage';
import UserSearchPage from './pages/UserSearchPage';
import AdminRepositoriesPage from './pages/AdminRepositoriesPage';

function App() {
  const [currentWorkspace, setCurrentWorkspace] = useState(null);
  const [workspaceData, setWorkspaceData] = useState(null);
  const [availableWorkspaces, setAvailableWorkspaces] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingWorkspaces, setLoadingWorkspaces] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [confirmDialog, setConfirmDialog] = useState(null);
  const [activeTab, setActiveTab] = useState('permissions'); // 'permissions' or 'audit'
  const [showAvatarsTestPage, setShowAvatarsTestPage] = useState(false); // Lisatud avatari testi jaoks
  const [showUserSearchPage, setShowUserSearchPage] = useState(false); // Kasutajaotsingu vaade
  const [showAdminRepositoriesPage, setShowAdminRepositoriesPage] = useState(false); // Administraatori repositooriumi vaade

  // User search functionality
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [userSearchResults, setUserSearchResults] = useState([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [allWorkspaceData, setAllWorkspaceData] = useState({}); // Cache for workspace data

  // Repository search functionality
  const [repositorySearchTerm, setRepositorySearchTerm] = useState('');
  const [repositorySearchResults, setRepositorySearchResults] = useState([]);
  const [repositorySearchLoading, setRepositorySearchLoading] = useState(false);

  // User filtering in workspace view
  const [userSortBy, setUserSortBy] = useState('name'); // 'name', 'permissions', 'lastActivity'
  const [userSortOrder, setUserSortOrder] = useState('asc');
  const [showUserFilters, setShowUserFilters] = useState(false);
  const [roleFilter, setRoleFilter] = useState('all'); // 'all', 'admin', 'write', 'read'
  const [repositoryFilter, setRepositoryFilter] = useState(''); // filter by repository name
  const [viewMode, setViewMode] = useState('table'); // 'grid' or 'table' - table is default (list view)
  const [sortBy, setSortBy] = useState('name'); // 'name', 'members', 'repos', 'lastAccessed', 'isPrivate'
  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' or 'desc'

  // Advanced filtering states
  const [privacyFilter, setPrivacyFilter] = useState('all'); // 'all', 'public', 'private'
  const [sizeFilter, setSizeFilter] = useState('all'); // 'all', 'small', 'medium', 'large'
  const [activityFilter, setActivityFilter] = useState('all'); // 'all', 'recent', 'old'
  const [showFilters, setShowFilters] = useState(false);

  // User-based filtering for workspaces
  const [userRoleFilter, setUserRoleFilter] = useState('all'); // 'all', 'admin', 'member', 'contributor'
  const [userCountFilter, setUserCountFilter] = useState('all'); // 'all', 'small', 'medium', 'large'
  const [sortByUsers, setSortByUsers] = useState(false); // sort by user count instead of name

  // Statistics view states
  const [activeStatsView, setActiveStatsView] = useState(null); // 'all', 'public', 'private', 'filtered', 'repositories'

  // Load workspaces on app start
  useEffect(() => {
    loadWorkspaces();
  }, [loadWorkspaces]);

  // Auto-search users when userSearchTerm changes
  useEffect(() => {
    const searchUser = async () => {
      if (!userSearchTerm || userSearchTerm.length < 2) {
        setUserSearchResults([]);
        return;
      }
      
      setUserSearchLoading(true);
      setUserSearchResults([]);
      
      try {
        const userResults = {};
        
        // Load data for each workspace
        for (const workspace of availableWorkspaces) {
          // Check if data is already cached
          if (!allWorkspaceData[workspace.slug]) {
            try {
              const workspaceData = await loadWorkspaceDataWithRepoCount(workspace);
              // Cache the data to avoid duplicate requests
              setAllWorkspaceData(prev => ({
                ...prev,
                [workspace.slug]: workspaceData
              }));
              
              // Search for user in this workspace
              if (workspaceData && workspaceData.permissions) {
                findUserInPermissions(workspaceData.permissions, workspace, userResults);
              }
            } catch (err) {
              console.error(`Viga tööruumi ${workspace.slug} andmete laadimisel:`, err);
            }
          } else {
            // Use cached data
            const overview = allWorkspaceData[workspace.slug];
            if (overview && overview.permissions) {
              findUserInPermissions(overview.permissions, workspace, userResults);
            }
          }
        }
        
        // Convert results to array for UI
        setUserSearchResults(Object.values(userResults));
      } catch (error) {
        console.error('Viga kasutaja otsimisel:', error);
      } finally {
        setUserSearchLoading(false);
      }
    };

    // Function to find user in permissions list
    const findUserInPermissions = (permissions, workspace, results) => {
      const searchTermLower = userSearchTerm.toLowerCase();
      
      const matchingPermissions = permissions.filter(
        permission => {
          const user = permission.user;
          return user && (
            (user.display_name && user.display_name.toLowerCase().includes(searchTermLower)) ||
            (user.nickname && user.nickname.toLowerCase().includes(searchTermLower)) ||
            (user.username && user.username.toLowerCase().includes(searchTermLower))
          );
        }
      );
      
      // Group by users
      matchingPermissions.forEach(permission => {
        const userId = permission.user?.uuid || permission.user?.account_id;
        if (!userId) return;
        
        if (!results[userId]) {
          results[userId] = {
            user: permission.user,
            workspaces: {}
          };
        }
        
        if (!results[userId].workspaces[workspace.slug]) {
          results[userId].workspaces[workspace.slug] = {
            name: workspace.name,
            slug: workspace.slug,
            permissions: []
          };
        }
        
        results[userId].workspaces[workspace.slug].permissions.push({
          repository: permission.repository,
          permission: permission.permission
        });
      });
    };

    if (userSearchTerm.length >= 2) {
      searchUser();
    } else {
      setUserSearchResults([]);
    }
  }, [userSearchTerm, availableWorkspaces, allWorkspaceData]);

  // Auto-search repositories when repositorySearchTerm changes
  useEffect(() => {
    const searchRepository = async () => {
      if (!repositorySearchTerm || repositorySearchTerm.length < 2) {
        setRepositorySearchResults([]);
        return;
      }
      
      setRepositorySearchLoading(true);
      setRepositorySearchResults([]);
      
      try {
        const repositoryResults = [];
        
        // Load data for each workspace and search repositories
        for (const workspace of availableWorkspaces) {
          // Check if data is already cached
          if (!allWorkspaceData[workspace.slug]) {
            try {
              const workspaceData = await loadWorkspaceDataWithRepoCount(workspace);
              // Cache the data to avoid duplicate requests
              setAllWorkspaceData(prev => ({
                ...prev,
                [workspace.slug]: workspaceData
              }));
              
              // Search for repositories in this workspace
              if (workspaceData && workspaceData.permissions) {
                findRepositoryInPermissions(workspaceData.permissions, workspace, repositoryResults);
              }
            } catch (err) {
              console.error(`Viga tööruumi ${workspace.slug} andmete laadimisel:`, err);
            }
          } else {
            // Use cached data
            const workspaceData = allWorkspaceData[workspace.slug];
            if (workspaceData && workspaceData.permissions) {
              findRepositoryInPermissions(workspaceData.permissions, workspace, repositoryResults);
            }
          }
        }
        
        // Remove duplicates and set results
        const uniqueRepositories = repositoryResults.filter((repo, index, self) => 
          index === self.findIndex(r => r.repository.uuid === repo.repository.uuid)
        );
        setRepositorySearchResults(uniqueRepositories);
      } catch (error) {
        console.error('Viga repositooriumi otsimisel:', error);
      } finally {
        setRepositorySearchLoading(false);
      }
    };

    // Function to find repositories in permissions list
    const findRepositoryInPermissions = (permissions, workspace, results) => {
      const searchTermLower = repositorySearchTerm.toLowerCase();
      
      const matchingPermissions = permissions.filter(
        permission => {
          const repository = permission.repository;
          return repository && (
            (repository.name && repository.name.toLowerCase().includes(searchTermLower)) ||
            (repository.slug && repository.slug.toLowerCase().includes(searchTermLower)) ||
            (repository.full_name && repository.full_name.toLowerCase().includes(searchTermLower))
          );
        }
      );
      
      // Group by repositories
      matchingPermissions.forEach(permission => {
        const repoId = permission.repository?.uuid;
        if (!repoId) return;
        
        // Check if repository already exists in results
        const existingRepo = results.find(r => r.repository.uuid === repoId);
        if (!existingRepo) {
          results.push({
            repository: permission.repository,
            workspace: {
              name: workspace.name,
              slug: workspace.slug
            },
            permissions: [permission],
            userCount: 1
          });
        } else {
          // Add permission to existing repository
          existingRepo.permissions.push(permission);
          existingRepo.userCount = existingRepo.permissions.length;
        }
      });
    };

    if (repositorySearchTerm.length >= 2) {
      searchRepository();
    } else {
      setRepositorySearchResults([]);
    }
  }, [repositorySearchTerm, availableWorkspaces, allWorkspaceData]);



  // Sort workspaces function
  const sortWorkspaces = (workspaces) => {
    return [...workspaces].sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'members':
          aValue = a.members?.size || 0;
          bValue = b.members?.size || 0;
          break;
        case 'repos':
          aValue = a.repositories?.size || 0;
          bValue = b.repositories?.size || 0;
          break;
        case 'lastAccessed':
          aValue = new Date(a.updated_on || a.created_on || 0);
          bValue = new Date(b.updated_on || b.created_on || 0);
          break;
        case 'isPrivate':
          aValue = a.is_private ? 1 : 0;
          bValue = b.is_private ? 1 : 0;
          break;
        case 'slug':
          aValue = a.slug.toLowerCase();
          bValue = b.slug.toLowerCase();
          break;
        case 'userCount': {
          // Enhanced user count estimation
          const getEstimatedUserCount = (workspace) => {
            if (workspace.is_private) return 1;
            if (workspace.name.toLowerCase().includes('enterprise')) return 50;
            if (workspace.name.toLowerCase().includes('team')) return 10;
            if (workspace.name.toLowerCase().includes('org')) return 20;
            return 5;
          };
          aValue = getEstimatedUserCount(a);
          bValue = getEstimatedUserCount(b);
          break;
        }
        case 'userRole': {
          // Sort by estimated user access level
          const getUserAccessWeight = (workspace) => {
            if (!workspace.is_private) return 3; // Admin access likely
            return 1; // Member access
          };
          aValue = getUserAccessWeight(a);
          bValue = getUserAccessWeight(b);
          break;
        }
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortBy === 'lastAccessed') {
        // For dates, newer should be first by default
        const comparison = bValue - aValue;
        return sortOrder === 'asc' ? -comparison : comparison;
      }

      if (typeof aValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortOrder === 'asc' ? comparison : -comparison;
      }

      const comparison = aValue - bValue;
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  };

  // Handle sort change
  const handleSort = (newSortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
  };

  // Enhanced filtering function with user-based filters
  const filterWorkspaces = (workspaces) => {
    return workspaces.filter(workspace => {
      // Text search
      const matchesSearch = searchTerm === '' ||
        workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workspace.slug.toLowerCase().includes(searchTerm.toLowerCase());

      // Privacy filter
      const matchesPrivacy = privacyFilter === 'all' ||
        (privacyFilter === 'private' && workspace.is_private) ||
        (privacyFilter === 'public' && !workspace.is_private);

      // Activity filter (based on creation date)
      const createdDate = new Date(workspace.created_on);
      const now = new Date();
      const daysDiff = (now - createdDate) / (1000 * 60 * 60 * 24);

      const matchesActivity = activityFilter === 'all' ||
        (activityFilter === 'recent' && daysDiff <= 365) ||
        (activityFilter === 'old' && daysDiff > 365);

      // Size filter (workspace complexity heuristic)
      const workspaceSize = workspace.is_private ? 'small' :
                           workspace.name.length > 20 ? 'large' : 'medium';

      const matchesSize = sizeFilter === 'all' || sizeFilter === workspaceSize;

      // User count filter (estimate based on workspace type and privacy)
      const estimatedUserCount = workspace.is_private ? 'small' :
                                workspace.name.includes('team') || workspace.name.includes('org') ? 'large' : 'medium';

      const matchesUserCount = userCountFilter === 'all' || userCountFilter === estimatedUserCount;

      // User role filter (for now, we'll use workspace type as proxy)
      const hasAdminAccess = !workspace.is_private; // Public workspaces typically have admin access
      const matchesUserRole = userRoleFilter === 'all' ||
        (userRoleFilter === 'admin' && hasAdminAccess) ||
        (userRoleFilter === 'member' && !hasAdminAccess);

      return matchesSearch && matchesPrivacy && matchesActivity && matchesSize && matchesUserCount && matchesUserRole;
    });
  };

  // Helper function to get highest role
  const getHighestRole = (roles) => {
    if (roles.includes('admin')) return 'admin';
    if (roles.includes('write')) return 'write';
    if (roles.includes('read')) return 'read';
    return 'none';
  };

  // Enhanced user filtering and sorting functions
  const filterAndSortUsers = (groupedUsers) => {
    const users = Object.values(groupedUsers);

    // Add workspace membership info to users
    const usersWithMembership = users.map(({ user, permissions }) => {
      const membership = workspaceData?.members?.find(m =>
        m.user?.uuid === user?.uuid || m.user?.account_id === user?.account_id
      );

      return {
        user,
        permissions,
        membership,
        workspaceRole: membership?.type || 'member', // workspace role
        repositoryRoles: permissions.map(p => p.permission), // repository permissions
        totalPermissions: permissions.length,
        highestRepoRole: getHighestRole(permissions.map(p => p.permission))
      };
    });

    // Filter users
    const filteredUsers = usersWithMembership.filter(({ user, repositoryRoles }) => {
      // Text search
      const matchesSearch = searchTerm === '' ||
        (user?.display_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user?.nickname || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user?.username || '').toLowerCase().includes(searchTerm.toLowerCase());

      // Role filter
      const matchesRole = roleFilter === 'all' ||
        repositoryRoles.includes(roleFilter) ||
        (roleFilter === 'admin' && repositoryRoles.includes('admin')) ||
        (roleFilter === 'write' && (repositoryRoles.includes('write') || repositoryRoles.includes('admin'))) ||
        (roleFilter === 'read' && repositoryRoles.some(role => ['read', 'write', 'admin'].includes(role)));

      return matchesSearch && matchesRole;
    });

    // Enhanced sorting
    return filteredUsers.sort((a, b) => {
      let aValue, bValue;

      switch (userSortBy) {
        case 'name':
          aValue = (a.user?.display_name || a.user?.nickname || '').toLowerCase();
          bValue = (b.user?.display_name || b.user?.nickname || '').toLowerCase();
          break;
        case 'permissions':
          aValue = a.totalPermissions;
          bValue = b.totalPermissions;
          break;
        case 'role': {
          const getRoleWeight = (role) => {
            switch (role) {
              case 'admin': return 3;
              case 'write': return 2;
              case 'read': return 1;
              default: return 0;
            }
          };
          aValue = getRoleWeight(a.highestRepoRole);
          bValue = getRoleWeight(b.highestRepoRole);
          break;
        }
        case 'workspaceRole': {
          // Workspace role sorting
          const getWorkspaceRoleWeight = (membership) => {
            if (!membership) return 0;
            // Add more workspace roles as needed
            return 1; // Basic member
          };
          aValue = getWorkspaceRoleWeight(a.membership);
          bValue = getWorkspaceRoleWeight(b.membership);
          break;
        }
        default:
          aValue = (a.user?.display_name || a.user?.nickname || '').toLowerCase();
          bValue = (b.user?.display_name || b.user?.nickname || '').toLowerCase();
      }

      if (typeof aValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return userSortOrder === 'asc' ? comparison : -comparison;
      }

      const comparison = aValue - bValue;
      return userSortOrder === 'asc' ? comparison : -comparison;
    });
  };



  const loadWorkspaces = async () => {
    setLoadingWorkspaces(true);
    try {
      const workspaces = await bitbucketApi.getUserWorkspaces();
      const workspaceList = workspaces.values || [];
      setAvailableWorkspaces(workspaceList);
      console.log('Loaded workspaces:', workspaceList.length);
      console.log('🔍 Avatar Debug - First workspace data:', workspaceList[0]);
      console.log('🔍 Avatar Debug - Avatar URLs from API:', workspaceList.slice(0, 3).map(w => ({
        name: w.name,
        avatarUrl: w.links?.avatar?.href
      })));

      // Load data for first few workspaces to populate repository statistics
      if (workspaceList.length > 0) {
        const workspacesToLoad = workspaceList.slice(0, Math.min(3, workspaceList.length));
        console.log(`Loading data for ${workspacesToLoad.length} workspaces to populate statistics...`);
        
        for (const workspace of workspacesToLoad) {
          try {
            const workspaceData = await loadWorkspaceDataWithRepoCount(workspace);
            setAllWorkspaceData(prev => ({
              ...prev,
              [workspace.slug]: workspaceData
            }));
          } catch (err) {
            console.error(`Error loading workspace ${workspace.slug} for statistics:`, err);
          }
        }
      }
    } catch (err) {
      console.error('Workspace loading failed:', err.message);
      // Show error if it's API key related
      if (err.message.includes('API võti') || err.message.includes('Autentimine')) {
        setError(`API error: ${err.message}. Check API key in backend/.env file.`);
      }
    } finally {
      setLoadingWorkspaces(false);
    }
  };

  // Helper function to load complete workspace data
  const loadWorkspaceDataWithRepoCount = async (workspace) => {
    try {
      // Load permissions overview
      const overview = await bitbucketApi.getWorkspaceOverview(workspace.slug);
      
      // Also get repository count for accurate statistics
      const repositories = await bitbucketApi.getWorkspaceRepositories(workspace.slug);
      const repositoryCount = repositories?.values?.length || 0;
      
      return {
        ...overview,
        repositoryCount
      };
    } catch (error) {
      console.error(`Error loading data for workspace ${workspace.slug}:`, error);
      // Return minimal data structure on error
      return {
        permissions: [],
        repositoryCount: 0
      };
    }
  };

  const handleWorkspaceSelect = async (workspace) => {
    setLoading(true);
    setError(null);

    try {
      // Load workspace data
      const overview = await bitbucketApi.getWorkspaceOverview(workspace);
      setWorkspaceData(overview);
      setCurrentWorkspace(workspace);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemovePermission = async (repoSlug, userId) => {
    if (!currentWorkspace) return;

    setLoading(true);
    try {
      await bitbucketApi.removeUserFromRepository(currentWorkspace, repoSlug, userId);

      // Värskenda andmeid
      const overview = await bitbucketApi.getWorkspaceOverview(currentWorkspace);
      setWorkspaceData(overview);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToWorkspaceSelector = () => {
    setCurrentWorkspace(null);
    setWorkspaceData(null);
    setError(null);
  };

  const handleRetry = () => {
    if (currentWorkspace) {
      handleWorkspaceSelect(currentWorkspace);
    } else {
      setError(null);
    }
  };

  // Kui näed seda brauseris, siis React töötab

  // Filtreeri õigused
  const filteredPermissions = workspaceData?.permissions?.filter(permission => {
    const user = permission.user;
    const repo = permission.repository;

    const matchesSearch = searchTerm === '' ||
      user?.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.nickname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      repo?.name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRepository = repositoryFilter === '' ||
      repo?.name?.toLowerCase().includes(repositoryFilter.toLowerCase());

    const matchesPermission = roleFilter === 'all' ||
      permission.permission === roleFilter;

    return matchesSearch && matchesPermission && matchesRepository;
  }) || [];

  // Grupeeri kasutajate kaupa
  const groupedByUser = {};
  filteredPermissions.forEach(permission => {
    const userId = permission.user?.uuid || permission.user?.account_id;
    if (!groupedByUser[userId]) {
      groupedByUser[userId] = {
        user: permission.user,
        permissions: []
      };
    }
    groupedByUser[userId].permissions.push(permission);
  });

  const getPermissionBadgeColor = (permission) => {
    switch (permission) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'write':
        return 'bg-yellow-100 text-yellow-800';
      case 'read':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRemoveClick = (permission) => {
    setConfirmDialog({
      user: permission.user,
      repository: permission.repository,
      permission: permission.permission,
      onConfirm: () => {
        handleRemovePermission(permission.repository.name, permission.user.account_id || permission.user.uuid);
        setConfirmDialog(null);
      },
      onCancel: () => setConfirmDialog(null)
    });
  };

  // Handle user search input changes
  const handleUserSearchChange = (e) => {
    setUserSearchTerm(e.target.value);
  };

  // Handle repository search input changes
  const handleRepositorySearchChange = (e) => {
    setRepositorySearchTerm(e.target.value);
  };

  // Handle statistics card clicks
  const handleStatsCardClick = (viewType) => {
    if (activeStatsView === viewType) {
      // If clicking the same card, reset to normal view
      setActiveStatsView(null);
      setPrivacyFilter('all');
      setSearchTerm('');
      setUserSearchTerm('');
      setRepositorySearchTerm('');
    } else {
      // Set the new active view
      setActiveStatsView(viewType);
      
      // Clear search terms to show filtered results
      setSearchTerm('');
      setUserSearchTerm('');
      setRepositorySearchTerm('');
      
      // Apply appropriate filters based on the card clicked
      switch (viewType) {
        case 'all':
          // Show all workspaces
          setPrivacyFilter('all');
          setSizeFilter('all');
          setActivityFilter('all');
          setUserRoleFilter('all');
          setUserCountFilter('all');
          break;
        case 'public':
          // Show only public workspaces
          setPrivacyFilter('public');
          break;
        case 'private':
          // Show only private workspaces
          setPrivacyFilter('private');
          break;
        case 'filtered':
          // Keep current filters but clear search
          setPrivacyFilter('all');
          break;
        case 'repositories':
          // Show all repositories view
          setRepositorySearchTerm('all-repositories'); // Special term to trigger repository view
          loadAllRepositories();
          break;
        default:
          break;
      }
    }
  };

  // Function to load all repositories
  const loadAllRepositories = async () => {
    setRepositorySearchLoading(true);
    setRepositorySearchResults([]);
    
    try {
      const repositoryResults = [];
      let failedWorkspaces = 0;
      
      // Load repositories for each workspace using the repositories API
      for (const workspace of availableWorkspaces) {
        try {
          console.log(`Loading repositories for workspace: ${workspace.slug}`);
          const repositories = await bitbucketApi.getWorkspaceRepositories(workspace.slug);
          
          if (repositories && repositories.values) {
            repositories.values.forEach(repo => {
              repositoryResults.push({
                repository: repo,
                workspace: workspace,
                userCount: 0, // Will be populated if needed
                permissions: []
              });
            });
            console.log(`Found ${repositories.values.length} repositories in ${workspace.slug}`);
          }
        } catch (err) {
          console.error(`Error loading repositories for workspace ${workspace.slug}:`, err);
          failedWorkspaces++;
        }
      }
      
      // Set all repositories as search results
      setRepositorySearchResults(repositoryResults);
      
      // Log summary for debugging
      console.log(`Repository loading summary:`);
      console.log(`- Loaded from ${availableWorkspaces.length - failedWorkspaces}/${availableWorkspaces.length} workspaces`);
      console.log(`- Total repositories found: ${repositoryResults.length}`);
      
      if (failedWorkspaces > 0) {
        console.warn(`Failed to load data from ${failedWorkspaces} workspace(s)`);
      }
    } catch (error) {
      console.error('Error loading all repositories:', error);
      setRepositorySearchResults([]); // Ensure we don't show stale data
    } finally {
      setRepositorySearchLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {showUserSearchPage ? (
        <UserSearchPage onBack={() => setShowUserSearchPage(false)} />
      ) : showAdminRepositoriesPage ? (
        <AdminRepositoriesPage onBack={() => setShowAdminRepositoriesPage(false)} />
      ) : showAvatarsTestPage ? (
        <div className="container mx-auto px-4 py-8">
          <div className="mb-4">
            <button
              onClick={() => setShowAvatarsTestPage(false)}
              className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              <ArrowLeft className="w-4 h-4" />
              Tagasi rakenduse vaatesse
            </button>
          </div>
          <BitbucketAvatarsTestPage />
        </div>
      ) : (
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {!currentWorkspace ? (
          // Kõik workspace'id kaartidena
          <div>
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Bitbucket Access Management
              </h1>
              <p className="text-gray-600 mb-4">
                Click on a workspace to view and manage user permissions
              </p>

              {/* Admin repositories button */}
              <div className="mt-2 flex gap-2 justify-center">
                <button 
                  onClick={() => setShowAdminRepositoriesPage(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700"
                >
                  <Shield className="w-4 h-4" />
                  Admin repositooriumid
                </button>
              </div>
            </div>



            {loadingWorkspaces && (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Laadin workspace'e...</p>
              </div>
            )}

            {error && (
              <div className="card bg-red-50 border-red-200 mb-6">
                <div className="flex items-center gap-3 text-red-600 mb-2">
                  <AlertTriangle className="w-5 h-5" />
                  <h3 className="font-medium">Viga workspace'ide laadimisel</h3>
                </div>
                <p className="text-red-800">{error}</p>
              </div>
            )}

            {availableWorkspaces.length > 0 && (
              <>
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Saadaolevad workspace'id
                    </h2>
                    <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                      {filterWorkspaces(availableWorkspaces).length} of {availableWorkspaces.length} workspaces
                    </span>
                  </div>

                  {/* Search and View Controls */}
                  <div className="mt-4 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                    <div className="flex flex-col sm:flex-row gap-4 flex-1">
                      {/* Workspace search */}
                      <div className="relative max-w-md flex-1">
                        <input
                          type="text"
                          placeholder="Search workspaces..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="input pl-10"
                        />
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      </div>

                      {/* User search */}
                      <div className="relative max-w-md flex-1">
                        <input
                          type="text"
                          placeholder="Otsi kasutajat..."
                          value={userSearchTerm}
                          onChange={handleUserSearchChange}
                          className="input pl-10"
                        />
                        <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      </div>

                      {/* Repository search */}
                      <div className="relative max-w-md flex-1">
                        <input
                          type="text"
                          placeholder="Otsi repositooriumi..."
                          value={repositorySearchTerm}
                          onChange={handleRepositorySearchChange}
                          className="input pl-10"
                        />
                        <GitBranch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      </div>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center gap-3">
                      {/* Filters Toggle */}
                      <button
                        onClick={() => setShowFilters(!showFilters)}
                        className={`px-3 py-2 text-sm font-medium border rounded-lg ${
                          showFilters
                            ? 'bg-primary-600 text-white border-primary-600'
                            : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                        }`}
                        title="Toggle filters"
                      >
                        <Filter className="w-4 h-4" />
                      </button>

                      {/* Refresh Button */}
                      <button
                        onClick={loadWorkspaces}
                        disabled={loadingWorkspaces}
                        className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Refresh workspaces"
                      >
                        <RefreshCw className={`w-4 h-4 ${loadingWorkspaces ? 'animate-spin' : ''}`} />
                      </button>

                      {/* View Mode Toggle */}
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">View:</span>
                        <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                          <button
                            onClick={() => setViewMode('table')}
                            className={`px-3 py-2 text-sm font-medium ${
                              viewMode === 'table'
                                ? 'bg-primary-600 text-white'
                                : 'bg-white text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            <List className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => setViewMode('grid')}
                            className={`px-3 py-2 text-sm font-medium ${
                              viewMode === 'grid'
                                ? 'bg-primary-600 text-white'
                                : 'bg-white text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            <Grid className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Advanced Filters Panel */}
                {showFilters && (
                  <div className="card bg-gray-50">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                      <button
                        onClick={() => {
                          setPrivacyFilter('all');
                          setSizeFilter('all');
                          setActivityFilter('all');
                          setUserRoleFilter('all');
                          setUserCountFilter('all');
                        }}
                        className="text-xs text-gray-500 hover:text-gray-700"
                      >
                        Clear all
                      </button>
                    </div>

                    <div className="space-y-6">
                      {/* Workspace Filters */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3">Workspace Filters</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* Privacy Filter */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">Privacy</label>
                            <select
                              value={privacyFilter}
                              onChange={(e) => setPrivacyFilter(e.target.value)}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="all">All workspaces</option>
                              <option value="public">Public only</option>
                              <option value="private">Private only</option>
                            </select>
                          </div>

                          {/* Activity Filter */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">Activity</label>
                            <select
                              value={activityFilter}
                              onChange={(e) => setActivityFilter(e.target.value)}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="all">All periods</option>
                              <option value="recent">Recent (last year)</option>
                              <option value="old">Older workspaces</option>
                            </select>
                          </div>

                          {/* Size Filter */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">Type</label>
                            <select
                              value={sizeFilter}
                              onChange={(e) => setSizeFilter(e.target.value)}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="all">All types</option>
                              <option value="small">Personal</option>
                              <option value="medium">Team</option>
                              <option value="large">Enterprise</option>
                            </select>
                          </div>
                        </div>
                      </div>

                      {/* User-Based Filters */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3">User & Access Filters</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {/* User Role Filter */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">My Access Level</label>
                            <select
                              value={userRoleFilter}
                              onChange={(e) => setUserRoleFilter(e.target.value)}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="all">All access levels</option>
                              <option value="admin">Admin access</option>
                              <option value="member">Member access</option>
                              <option value="contributor">Contributor only</option>
                            </select>
                          </div>

                          {/* User Count Filter */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">Team Size</label>
                            <select
                              value={userCountFilter}
                              onChange={(e) => setUserCountFilter(e.target.value)}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="all">All team sizes</option>
                              <option value="small">Small teams (1-5)</option>
                              <option value="medium">Medium teams (6-20)</option>
                              <option value="large">Large teams (20+)</option>
                            </select>
                          </div>

                          {/* Sort by Users */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 mb-2">Sort Priority</label>
                            <select
                              value={sortByUsers ? 'users' : 'name'}
                              onChange={(e) => setSortByUsers(e.target.value === 'users')}
                              className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            >
                              <option value="name">Sort by name</option>
                              <option value="users">Sort by user count</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Workspace Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                  <div 
                    onClick={() => handleStatsCardClick('all')}
                    className={`card text-center cursor-pointer transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                      activeStatsView === 'all' ? 'ring-2 ring-primary-500 bg-primary-50' : ''
                    }`}
                  >
                    <div className="text-2xl font-bold text-primary-600 mb-1">
                      {availableWorkspaces.length}
                    </div>
                    <div className="text-sm text-gray-600">Total Workspaces</div>
                  </div>

                  <div 
                    onClick={() => handleStatsCardClick('public')}
                    className={`card text-center cursor-pointer transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                      activeStatsView === 'public' ? 'ring-2 ring-green-500 bg-green-50' : ''
                    }`}
                  >
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {availableWorkspaces.filter(w => !w.is_private).length}
                    </div>
                    <div className="text-sm text-gray-600">Public</div>
                  </div>

                  <div 
                    onClick={() => handleStatsCardClick('private')}
                    className={`card text-center cursor-pointer transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                      activeStatsView === 'private' ? 'ring-2 ring-yellow-500 bg-yellow-50' : ''
                    }`}
                  >
                    <div className="text-2xl font-bold text-yellow-600 mb-1">
                      {availableWorkspaces.filter(w => w.is_private).length}
                    </div>
                    <div className="text-sm text-gray-600">Private</div>
                  </div>

                  <div 
                    onClick={() => handleStatsCardClick('filtered')}
                    className={`card text-center cursor-pointer transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                      activeStatsView === 'filtered' ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    }`}
                  >
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {filterWorkspaces(availableWorkspaces).length}
                    </div>
                    <div className="text-sm text-gray-600">Filtered</div>
                  </div>

                  <div 
                    onClick={() => handleStatsCardClick('repositories')}
                    className={`card text-center cursor-pointer transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                      activeStatsView === 'repositories' ? 'ring-2 ring-purple-500 bg-purple-50' : ''
                    }`}
                  >
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      {(() => {
                        // If we have repository search results, show that count
                        if (repositorySearchTerm === 'all-repositories' && repositorySearchResults.length > 0) {
                          return repositorySearchResults.length;
                        }
                        
                        // If we have cached repository counts from workspace data, use those
                        let totalRepositories = 0;
                        availableWorkspaces.forEach(workspace => {
                          if (allWorkspaceData[workspace.slug] && allWorkspaceData[workspace.slug].repositoryCount !== undefined) {
                            totalRepositories += allWorkspaceData[workspace.slug].repositoryCount;
                            console.log(`Adding ${allWorkspaceData[workspace.slug].repositoryCount} repositories from ${workspace.slug}`);
                          }
                        });
                        
                        console.log(`Total repositories calculated: ${totalRepositories} from ${Object.keys(allWorkspaceData).length} loaded workspaces`);
                        
                        if (totalRepositories > 0) {
                          return totalRepositories;
                        }
                        
                        // Show loading if no data is available yet
                        return '...';
                      })()}
                    </div>
                    <div className="text-sm text-gray-600">Total Repositories</div>
                  </div>
                </div>

                {/* Active Statistics View Indicator */}
                {activeStatsView && (
                  <div className="mb-6">
                    <div className={`card border-l-4 bg-gray-50 ${
                      activeStatsView === 'all' ? 'border-l-primary-500' :
                      activeStatsView === 'public' ? 'border-l-green-500' :
                      activeStatsView === 'private' ? 'border-l-yellow-500' :
                      activeStatsView === 'filtered' ? 'border-l-blue-500' :
                      'border-l-purple-500'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            activeStatsView === 'all' ? 'bg-primary-500' :
                            activeStatsView === 'public' ? 'bg-green-500' :
                            activeStatsView === 'private' ? 'bg-yellow-500' :
                            activeStatsView === 'filtered' ? 'bg-blue-500' :
                            'bg-purple-500'
                          }`}></div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              Active filter: {
                                activeStatsView === 'all' ? 'All Workspaces' :
                                activeStatsView === 'public' ? 'Public Workspaces' :
                                activeStatsView === 'private' ? 'Private Workspaces' :
                                activeStatsView === 'filtered' ? 'Filtered Results' :
                                'Repository View'
                              }
                            </p>
                            <p className="text-xs text-gray-600">
                              {activeStatsView === 'repositories' ? 
                                'Showing all repositories across workspaces' :
                                'Click the same statistics card again to remove the filter'
                              }
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleStatsCardClick(activeStatsView)}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title="Clear filter"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Repository Statistics - Show when repository search is active */}
                {repositorySearchTerm && repositorySearchTerm.length >= 2 && repositorySearchResults.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="card text-center bg-green-50 border-green-200">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {repositorySearchResults.length}
                      </div>
                      <div className="text-sm text-gray-600">
                        {repositorySearchTerm === 'all-repositories' ? 'Repositooriume kokku' : 'Leitud repositooriume'}
                      </div>
                    </div>

                    <div className="card text-center bg-green-50 border-green-200">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {repositorySearchResults.filter(r => !r.repository?.is_private).length}
                      </div>
                      <div className="text-sm text-gray-600">Public repod</div>
                    </div>

                    <div className="card text-center bg-green-50 border-green-200">
                      <div className="text-2xl font-bold text-red-600 mb-1">
                        {repositorySearchResults.filter(r => r.repository?.is_private).length}
                      </div>
                      <div className="text-sm text-gray-600">Private repod</div>
                    </div>

                    <div className="card text-center bg-green-50 border-green-200">
                      <div className="text-2xl font-bold text-purple-600 mb-1">
                        {repositorySearchResults.reduce((sum, r) => sum + r.userCount, 0)}
                      </div>
                      <div className="text-sm text-gray-600">Kasutajate ligipääsud</div>
                    </div>
                  </div>
                )}

                {/* User Search Results */}
                {userSearchTerm && userSearchTerm.length >= 2 && (
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Kasutaja otsingu tulemused "{userSearchTerm}"
                      </h3>
                      {userSearchLoading && (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                          Otsin...
                        </div>
                      )}
                    </div>

                    {userSearchResults.length > 0 ? (
                      <div className="space-y-4">
                        {userSearchResults.map((userResult, index) => (
                          <div key={userResult.user?.uuid || index} className="card bg-blue-50 border-blue-200">
                            <div className="flex items-start gap-4">
                              <Avatar
                                src={userResult.user?.links?.avatar?.href}
                                alt={`${userResult.user?.display_name || userResult.user?.username} avatar`}
                                type="user"
                                size="md"
                                fallbackName={userResult.user?.display_name || userResult.user?.username}
                              />
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900 mb-1">
                                  {userResult.user?.display_name || userResult.user?.username}
                                </h4>
                                {userResult.user?.nickname && (
                                  <p className="text-sm text-gray-600 mb-2">@{userResult.user.nickname}</p>
                                )}
                                
                                <div className="space-y-2">
                                  <p className="text-sm font-medium text-gray-700">
                                    Liige järgmistes workspace'ides:
                                  </p>
                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    {Object.entries(userResult.workspaces).map(([workspaceSlug, workspaceInfo]) => (
                                      <div key={workspaceSlug} className="bg-white rounded-lg border border-gray-200 p-3">
                                        <div className="flex items-center justify-between mb-2">
                                          <h5 className="font-medium text-sm text-gray-900 truncate">
                                            {workspaceInfo.name}
                                          </h5>
                                          <button
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleWorkspaceSelect(workspaceSlug);
                                            }}
                                            className="text-xs px-2 py-1 bg-primary-100 text-primary-700 rounded hover:bg-primary-200"
                                          >
                                            Vaata
                                          </button>
                                        </div>
                                        <div className="text-xs text-gray-600">
                                          {workspaceInfo.permissions.length} repositooriumi
                                        </div>
                                        <div className="flex flex-wrap gap-1 mt-1">
                                          {[...new Set(workspaceInfo.permissions.map(p => p.permission))].map(permission => (
                                            <span
                                              key={permission}
                                              className={`inline-block px-1.5 py-0.5 text-xs rounded ${
                                                permission === 'admin' ? 'bg-red-100 text-red-700' :
                                                permission === 'write' ? 'bg-yellow-100 text-yellow-700' :
                                                'bg-gray-100 text-gray-700'
                                              }`}
                                            >
                                              {permission}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : !userSearchLoading ? (
                      <div className="text-center py-8 text-gray-500">
                        <Users className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                        <p>Kasutajat "{userSearchTerm}" ei leitud ühestki workspace'ist</p>
                      </div>
                    ) : null}
                  </div>
                )}

                {/* Repository Search Results */}
                {repositorySearchTerm && repositorySearchTerm.length >= 2 && (
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {repositorySearchTerm === 'all-repositories' ? 
                          'Kõik repositooriumid' : 
                          `Repositooriumide otsingu tulemused "${repositorySearchTerm}"`
                        }
                      </h3>
                      {repositorySearchLoading && (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                          {repositorySearchTerm === 'all-repositories' ? 'Laadin kõiki repositooriume...' : 'Otsin...'}
                        </div>
                      )}
                    </div>

                    {repositorySearchResults.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {repositorySearchResults.map((repoResult, index) => (
                          <div key={repoResult.repository?.uuid || index} className="card bg-green-50 border-green-200">
                            <div className="flex items-start gap-3">
                              <div className="flex-shrink-0">
                                <GitBranch className="w-8 h-8 text-green-600" />
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-gray-900 truncate">
                                    {repoResult.repository?.name}
                                  </h4>
                                  <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                                    repoResult.repository?.is_private 
                                      ? 'bg-red-100 text-red-700' 
                                      : 'bg-green-100 text-green-700'
                                  }`}>
                                    {repoResult.repository?.is_private ? 'Private' : 'Public'}
                                  </span>
                                </div>
                                
                                <div className="text-sm text-gray-600 mb-2">
                                  <span className="font-medium">Workspace:</span> {repoResult.workspace.name}
                                </div>
                                
                                <div className="text-sm text-gray-600 mb-2">
                                  <span className="font-medium">{repoResult.userCount}</span> kasutajat
                                </div>
                                
                                <div className="flex flex-wrap gap-1 mb-3">
                                  {[...new Set(repoResult.permissions.map(p => p.permission))].map(permission => (
                                    <span
                                      key={permission}
                                      className={`inline-block px-1.5 py-0.5 text-xs rounded ${
                                        permission === 'admin' ? 'bg-red-100 text-red-700' :
                                        permission === 'write' ? 'bg-yellow-100 text-yellow-700' :
                                        'bg-gray-100 text-gray-700'
                                      }`}
                                    >
                                      {permission}
                                    </span>
                                  ))}
                                </div>
                                
                                <div className="flex gap-2">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleWorkspaceSelect(repoResult.workspace.slug);
                                    }}
                                    className="text-xs px-2 py-1 bg-primary-100 text-primary-700 rounded hover:bg-primary-200"
                                  >
                                    Vaata workspace
                                  </button>
                                  {repoResult.repository?.links?.html?.href && (
                                    <a
                                      href={repoResult.repository.links.html.href}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                                    >
                                      Ava Bitbucketis
                                    </a>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : !repositorySearchLoading ? (
                      <div className="text-center py-8 text-gray-500">
                        <GitBranch className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                        <p>
                          {repositorySearchTerm === 'all-repositories' ? 
                            'Ühtegi repositooriumi ei leitud' : 
                            `Repositooriumi "${repositorySearchTerm}" ei leitud ühestki workspace'ist`
                          }
                        </p>
                      </div>
                    ) : null}
                  </div>
                )}

                {/* Workspace Views */}
                {viewMode === 'grid' ? (
                  /* Grid View */
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {sortWorkspaces(filterWorkspaces(availableWorkspaces)).map((workspace) => (
                      <div
                        key={workspace.uuid}
                        onClick={() => handleWorkspaceSelect(workspace.slug)}
                        className="card hover:shadow-lg transition-all duration-200 cursor-pointer border-2 border-transparent hover:border-primary-200 hover:scale-105 fade-in"
                      >
                        <div className="flex items-center gap-3 mb-3">
                          <Avatar
                            src={workspace.links?.avatar?.href}
                            alt={`${workspace.name} avatar`}
                            type="workspace"
                            size="md"
                            fallbackName={workspace.name}
                          />
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 truncate">
                              {workspace.name}
                            </h3>
                            <p className="text-sm text-gray-500 truncate">
                              @{workspace.slug}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span className={`px-2 py-1 rounded-full ${workspace.is_private ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'}`}>
                              {workspace.is_private ? 'Private' : 'Public'}
                            </span>
                            <span>
                              {new Date(workspace.created_on).getFullYear()}
                            </span>
                          </div>

                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3" />
                              {(() => {
                                const getEstimatedUserCount = (workspace) => {
                                  if (workspace.is_private) return 1;
                                  if (workspace.name.toLowerCase().includes('enterprise')) return 50;
                                  if (workspace.name.toLowerCase().includes('team')) return 10;
                                  if (workspace.name.toLowerCase().includes('org')) return 20;
                                  return 5;
                                };
                                return `~${getEstimatedUserCount(workspace)}`;
                              })()}
                            </span>

                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              workspace.is_private
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {workspace.is_private ? 'Member' : 'Admin'}
                            </span>
                          </div>
                        </div>

                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex items-center justify-center text-primary-600 text-sm font-medium hover:text-primary-700 transition-colors">
                            <Shield className="w-4 h-4 mr-1" />
                            Manage Permissions →
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  /* Table View */
                  <div className="card overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th
                              onClick={() => handleSort('name')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                Name
                                {sortBy === 'name' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th
                              onClick={() => handleSort('slug')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                Slug
                                {sortBy === 'slug' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th
                              onClick={() => handleSort('isPrivate')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                Privacy
                                {sortBy === 'isPrivate' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th
                              onClick={() => handleSort('lastAccessed')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                Created
                                {sortBy === 'lastAccessed' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th
                              onClick={() => handleSort('userCount')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                Users
                                {sortBy === 'userCount' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th
                              onClick={() => handleSort('userRole')}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
                            >
                              <div className="flex items-center gap-1">
                                My Role
                                {sortBy === 'userRole' && (
                                  sortOrder === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                                )}
                              </div>
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {sortWorkspaces(filterWorkspaces(availableWorkspaces)).map((workspace) => (
                            <tr
                              key={workspace.uuid}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleWorkspaceSelect(workspace.slug)}
                            >
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <Avatar
                                    src={workspace.links?.avatar?.href}
                                    alt={`${workspace.name} avatar`}
                                    type="workspace"
                                    size="sm"
                                    fallbackName={workspace.name}
                                    className="mr-3"
                                  />
                                  <div className="text-sm font-medium text-gray-900">{workspace.name}</div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500">@{workspace.slug}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {workspace.is_private ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Private
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Public
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {workspace.created_on ? new Date(workspace.created_on).toLocaleDateString() : 'N/A'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {(() => {
                                  const getEstimatedUserCount = (workspace) => {
                                    if (workspace.is_private) return 1;
                                    if (workspace.name.toLowerCase().includes('enterprise')) return 50;
                                    if (workspace.name.toLowerCase().includes('team')) return 10;
                                    if (workspace.name.toLowerCase().includes('org')) return 20;
                                    return 5;
                                  };
                                  return `~${getEstimatedUserCount(workspace)}`;
                                })()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                {workspace.is_private ? (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Member
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Admin
                                  </span>
                                )}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleWorkspaceSelect(workspace.slug);
                                  }}
                                  className="text-primary-600 hover:text-primary-900"
                                >
                                  View Permissions
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* No search results */}
                {filterWorkspaces(availableWorkspaces).length === 0 && (searchTerm || privacyFilter !== 'all' || activityFilter !== 'all' || sizeFilter !== 'all') && (
                  <div className="text-center py-12">
                    <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No workspaces found</h3>
                    <p className="text-gray-600 mb-4">
                      Try adjusting your search terms or filters
                    </p>
                    <button
                      onClick={() => {
                        setSearchTerm('');
                        setPrivacyFilter('all');
                        setActivityFilter('all');
                        setSizeFilter('all');
                        setUserRoleFilter('all');
                        setUserCountFilter('all');
                        setSortByUsers(false);
                      }}
                      className="btn btn-secondary"
                    >
                      Clear all filters
                    </button>
                  </div>
                )}
              </>
            )}



            {/* Hoiatus */}
            <div className="mt-8 max-w-2xl mx-auto">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800 mb-1">Hoiatus</h4>
                    <p className="text-sm text-yellow-700">
                      See tööriist võimaldab eemaldada kasutajate ligipääsu repositooriumitele.
                      Kõik toimingud on pöördumatud. Kasuta ettevaatlikult!
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Peamine vaade
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBackToWorkspaceSelector}
                  className="btn btn-secondary inline-flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {currentWorkspace}
                  </h1>
                  {workspaceData && (
                    <p className="text-gray-600">
                      {workspaceData.totalMembers} members, {workspaceData.totalRepositories} repositories, {workspaceData.totalPermissions} permissions
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={() => handleWorkspaceSelect(currentWorkspace)}
                  disabled={loading}
                  className="btn btn-secondary inline-flex items-center gap-2"
                  title="Refresh workspace data"
                >
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>

                <div className="flex items-center gap-2 text-gray-500">
                  <Settings className="w-5 h-5" />
                  <span className="text-sm">Access Management</span>
                </div>
              </div>
            </div>

            {/* Tabs */}
            {workspaceData && !loading && !error && (
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  <button
                    onClick={() => setActiveTab('permissions')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'permissions'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      Repository Permissions
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        {workspaceData.totalPermissions}
                      </span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('members')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'members'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      Workspace Members
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        {workspaceData.totalMembers}
                      </span>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('audit')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'audit'
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <History className="w-4 h-4" />
                      Audit Log
                    </div>
                  </button>
                </nav>
              </div>
            )}

            {/* Enhanced Statistics */}
            {workspaceData && !loading && !error && (
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {[
                  {
                    label: 'Members',
                    value: workspaceData.totalMembers || 0,
                    icon: Users,
                    color: 'text-blue-600',
                    bgColor: 'bg-blue-100',
                    description: `${workspaceData.members?.length || 0} workspace members`
                  },
                  {
                    label: 'Repositories',
                    value: workspaceData.totalRepositories || 0,
                    icon: GitBranch,
                    color: 'text-green-600',
                    bgColor: 'bg-green-100',
                    description: 'Total repositories'
                  },
                  {
                    label: 'Permissions',
                    value: workspaceData.totalPermissions || 0,
                    icon: Shield,
                    color: 'text-purple-600',
                    bgColor: 'bg-purple-100',
                    description: 'Repository permissions'
                  },
                  {
                    label: 'Projects',
                    value: workspaceData.totalProjects || 0,
                    icon: Building,
                    color: 'text-orange-600',
                    bgColor: 'bg-orange-100',
                    description: 'Active projects'
                  },
                  {
                    label: 'Active Users',
                    value: Object.keys(groupedByUser).length,
                    icon: Users,
                    color: 'text-indigo-600',
                    bgColor: 'bg-indigo-100',
                    description: 'Users with permissions'
                  }
                ].map((stat) => {
                  const Icon = stat.icon;
                  return (
                    <div key={stat.label} className="card text-center hover:shadow-md transition-shadow">
                      <div className={`w-12 h-12 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-3`}>
                        <Icon className={`w-6 h-6 ${stat.color}`} />
                      </div>
                      <div className="text-2xl font-bold text-gray-900 mb-1">
                        {stat.value}
                      </div>
                      <div className="text-sm text-gray-600 mb-1">
                        {stat.label}
                      </div>
                      <div className="text-xs text-gray-500">
                        {stat.description}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Sisu */}
            {loading && (
              <div className="card text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading workspace data...</p>
                <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
              </div>
            )}

            {error && (
              <div className="card">
                <div className="flex items-center gap-3 text-red-600 mb-4">
                  <AlertTriangle className="w-6 h-6" />
                  <h3 className="text-lg font-medium">Viga</h3>
                </div>
                <p className="text-gray-700 mb-4">{error}</p>
                <button
                  onClick={handleRetry}
                  className="btn btn-primary"
                >
                  Proovi uuesti
                </button>
              </div>
            )}

            {workspaceData && !loading && !error && (
              <>
                {/* Filtrid */}
                <div className="space-y-4">
                  {/* Search and Quick Filters */}
                  <div className="card">
                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                      <div className="flex-1">
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="Search users or repositories..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="input pl-10"
                          />
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        {/* User Filters Toggle */}
                        <button
                          onClick={() => setShowUserFilters(!showUserFilters)}
                          className={`px-3 py-2 text-sm font-medium border rounded-lg ${
                            showUserFilters
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                          }`}
                          title="Toggle user filters"
                        >
                          <Filter className="w-4 h-4" />
                        </button>

                        {/* Quick Role Filter */}
                        <select
                          value={roleFilter}
                          onChange={(e) => setRoleFilter(e.target.value)}
                          className="input sm:w-32"
                        >
                          <option value="all">All roles</option>
                          <option value="admin">Admin</option>
                          <option value="write">Write</option>
                          <option value="read">Read</option>
                        </select>
                      </div>
                    </div>

                    {/* Enhanced Filter Results Statistics */}
                    <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-3">
                      <div className="bg-blue-50 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-blue-600">{filteredPermissions.length}</div>
                        <div className="text-xs text-blue-700">Filtered Permissions</div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-green-600">{Object.keys(groupedByUser).length}</div>
                        <div className="text-xs text-green-700">Active Users</div>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-purple-600">
                          {filteredPermissions.filter(p => p.permission === 'admin').length}
                        </div>
                        <div className="text-xs text-purple-700">Admin Access</div>
                      </div>
                      <div className="bg-yellow-50 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-yellow-600">
                          {filteredPermissions.filter(p => p.permission === 'write').length}
                        </div>
                        <div className="text-xs text-yellow-700">Write Access</div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-gray-600">
                          {new Set(filteredPermissions.map(p => p.repository?.name)).size}
                        </div>
                        <div className="text-xs text-gray-700">Repositories</div>
                      </div>
                    </div>
                  </div>

                  {/* Advanced User Filters */}
                  {showUserFilters && (
                    <div className="card bg-gray-50">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-sm font-medium text-gray-900">User Filters & Sorting</h4>
                        <button
                          onClick={() => {
                            setRoleFilter('all');
                            setUserSortBy('name');
                            setUserSortOrder('asc');
                          }}
                          className="text-xs text-gray-500 hover:text-gray-700"
                        >
                          Reset
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Repository Filter */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-2">Repository filter</label>
                          <input
                            type="text"
                            placeholder="Filter by repository..."
                            value={repositoryFilter}
                            onChange={(e) => setRepositoryFilter(e.target.value)}
                            className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                          />
                        </div>

                        {/* Sort By */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-2">Sort by</label>
                          <select
                            value={userSortBy}
                            onChange={(e) => setUserSortBy(e.target.value)}
                            className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                          >
                            <option value="name">Name</option>
                            <option value="permissions">Permission count</option>
                            <option value="role">Highest repository role</option>
                            <option value="workspaceRole">Workspace role</option>
                          </select>
                        </div>

                        {/* Sort Order */}
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-2">Order</label>
                          <select
                            value={userSortOrder}
                            onChange={(e) => setUserSortOrder(e.target.value)}
                            className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                          >
                            <option value="asc">Ascending</option>
                            <option value="desc">Descending</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Kasutajate nimekiri */}
                {Object.keys(groupedByUser).length === 0 ? (
                  <div className="card text-center py-12">
                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No permissions found</h3>
                    <p className="text-gray-600 mb-4">
                      {searchTerm || roleFilter !== 'all' || repositoryFilter
                        ? 'No permissions match your current filters.'
                        : 'This workspace has no user permissions configured.'}
                    </p>
                    {(searchTerm || roleFilter !== 'all' || repositoryFilter) && (
                      <button
                        onClick={() => {
                          setSearchTerm('');
                          setRoleFilter('all');
                          setRepositoryFilter('');
                        }}
                        className="btn btn-secondary"
                      >
                        Clear filters
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filterAndSortUsers(groupedByUser).map(({ user, permissions: userPermissions, membership, highestRepoRole, totalPermissions }, index) => (
                      <div
                        key={user?.uuid || user?.account_id}
                        className="card fade-in hover:shadow-lg transition-all duration-200"
                        style={{ animationDelay: `${index * 0.1}s` }}
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <Avatar
                              src={user?.links?.avatar?.href}
                              alt={`${user?.display_name || user?.nickname} avatar`}
                              type="user"
                              size="md"
                              fallbackName={user?.display_name || user?.nickname}
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-medium text-gray-900">{user?.display_name || user?.nickname || 'Unknown user'}</h3>
                                {/* Workspace membership badge */}
                                {membership && (
                                  <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                    Member
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">@{user?.nickname || user?.username || 'unknown'}</p>
                              {/* User stats */}
                              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                <span>{totalPermissions} repositories</span>
                                <span>Highest: <span className={`font-medium ${getPermissionBadgeColor(highestRepoRole).replace('bg-', 'text-').replace('-100', '-600')}`}>{highestRepoRole}</span></span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-500 mb-1">
                              {totalPermissions} permission{totalPermissions !== 1 ? 's' : ''}
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(highestRepoRole)}`}>
                              {highestRepoRole}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          {userPermissions
                            .filter(permission => {
                              // Apply repository filter
                              return repositoryFilter === '' ||
                                permission.repository?.name?.toLowerCase().includes(repositoryFilter.toLowerCase());
                            })
                            .map((permission, index) => (
                            <div
                              key={`${permission.repository?.uuid || index}-${user?.uuid || user?.account_id}`}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                            >
                              <div className="flex items-center gap-3 flex-1">
                                <GitBranch className="w-4 h-4 text-gray-400" />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium text-gray-900">
                                      {permission.repository?.name || 'Unknown repository'}
                                    </span>
                                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPermissionBadgeColor(permission.permission)}`}>
                                      {permission.permission}
                                    </span>
                                  </div>
                                  {permission.repository?.full_name && (
                                    <p className="text-xs text-gray-500 mt-1">
                                      {permission.repository.full_name}
                                    </p>
                                  )}
                                </div>
                              </div>

                              <button
                                onClick={() => handleRemoveClick(permission)}
                                disabled={loading}
                                className="btn btn-danger text-sm px-3 py-1 ml-3"
                                title="Remove permission"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* Kinnituse dialoog */}
        {confirmDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Eemalda kasutaja õigus</h3>
                <button
                  onClick={confirmDialog.onCancel}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-2">
                  <p>Kas oled kindel, et soovid eemaldada järgmise õiguse?</p>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p><strong>Kasutaja:</strong> {confirmDialog.user?.display_name}</p>
                    <p><strong>Repositoorium:</strong> {confirmDialog.repository?.name}</p>
                    <p><strong>Õigus:</strong> {confirmDialog.permission}</p>
                  </div>
                  <div className="flex items-center gap-2 text-amber-600">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="text-sm">See tegevus on pöördumatu!</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 p-6 border-t border-gray-200">
                <button
                  onClick={confirmDialog.onCancel}
                  className="btn btn-secondary flex-1"
                >
                  Tühista
                </button>
                <button
                  onClick={confirmDialog.onConfirm}
                  className="btn btn-danger flex-1"
                >
                  Eemalda õigus
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Workspace Members Tab */}
        {workspaceData && !loading && !error && activeTab === 'members' && (
          <div className="space-y-6">
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Workspace Members</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {workspaceData.members?.map((member, index) => (
                  <div
                    key={member.user?.uuid || member.user?.account_id || index}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <Avatar
                        src={member.user?.links?.avatar?.href}
                        alt={`${member.user?.display_name || member.user?.nickname} avatar`}
                        type="user"
                        size="md"
                        fallbackName={member.user?.display_name || member.user?.nickname}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">
                          {member.user?.display_name || member.user?.nickname || 'Unknown user'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          @{member.user?.nickname || member.user?.username || 'unknown'}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Workspace Role:</span>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          Member
                        </span>
                      </div>

                      {/* Repository permissions count for this member */}
                      {(() => {
                        const memberPermissions = workspaceData.permissions?.filter(p =>
                          p.user?.uuid === member.user?.uuid || p.user?.account_id === member.user?.account_id
                        ) || [];
                        return (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Repository Access:</span>
                            <span className="text-gray-900 font-medium">
                              {memberPermissions.length} repositories
                            </span>
                          </div>
                        );
                      })()}

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Account ID:</span>
                        <span className="text-xs text-gray-500 font-mono">
                          {member.user?.account_id?.substring(0, 12)}...
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {(!workspaceData.members || workspaceData.members.length === 0) && (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No members found</h3>
                  <p className="text-gray-600">This workspace has no members configured.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Audit Log Tab */}
        {workspaceData && !loading && !error && activeTab === 'audit' && (
          <AuditLog workspace={currentWorkspace} />
        )}
      </div>
      )}
    </div>
  );
}

export default App;
