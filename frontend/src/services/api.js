import axios from 'axios';

const API_BASE_URL = 'http://localhost:3002/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API teenused
export const bitbucketApi = {
  // Kasutaja workspace'id
  getUserWorkspaces: async () => {
    const response = await api.get('/bitbucket/workspaces');
    return response.data;
  },
  // Workspace info
  getWorkspaceInfo: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}`);
    return response.data;
  },

  // Workspace liikmed
  getWorkspaceMembers: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/members`);
    return response.data;
  },

  // Workspace repositooriumid
  getWorkspaceRepositories: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/repositories`);
    return response.data;
  },

  // Kõik repositooriumi õigused workspace'is
  getWorkspacePermissions: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/permissions`);
    return response.data;
  },

  // Konkreetse repositooriumi kasutajate õigused
  getRepositoryUsers: async (workspace, repoSlug) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/repository/${repoSlug}/users`);
    return response.data;
  },

  // Kasutaja õiguse eemaldamine repositooriumist
  removeUserFromRepository: async (workspace, repoSlug, userId) => {
    const response = await api.delete(`/bitbucket/workspace/${workspace}/repository/${repoSlug}/user/${userId}`);
    return response.data;
  },

  // Kasutaja eemaldamine kogu workspace'ist (kõik repositooriumid)
  removeUserFromWorkspace: async (workspace, userId) => {
    const response = await api.delete(`/bitbucket/workspace/${workspace}/user/${userId}`);
    return response.data;
  },
  
  // Kasutaja õiguse muutmine repositooriumis
  updateUserRepositoryPermission: async (workspace, repoSlug, userId, permission) => {
    const response = await api.put(`/bitbucket/workspace/${workspace}/repository/${repoSlug}/user/${userId}`, { 
      permission 
    });
    return response.data;
  },

  // Workspace projektid
  getWorkspaceProjects: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/projects`);
    return response.data;
  },

  // Koondatud ülevaade
  getWorkspaceOverview: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/overview`);
    return response.data;
  },

  // Workspace statistika andmebaasist
  getWorkspaceStats: async (workspace) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/stats`);
    return response.data;
  },

  // Audit logi
  getAuditLog: async (workspace, limit = 100) => {
    const response = await api.get(`/bitbucket/workspace/${workspace}/audit?limit=${limit}`);
    return response.data;
  },

  // Salvestatud workspace'id
  getSavedWorkspaces: async () => {
    const response = await api.get('/bitbucket/workspaces/saved');
    return response.data;
  },
  
  // Kasutaja info hankimine
  getUserInfo: async (userId) => {
    try {
      // Puhasta UUID juhul kui see sisaldab loogelisi sulge
      const cleanId = userId.replace(/[{}]/g, '');
      const response = await api.get(`/bitbucket/user/${cleanId}`);
      return response.data;
    } catch (error) {
      console.error(`Viga kasutaja ${userId} info hankimisel:`, error);
      return null;
    }
  },
};

// Vigade käsitlemine
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const message = error.response?.data?.error || error.message || 'Teadmata viga';
    console.error('API viga:', message);
    throw new Error(message);
  }
);

export default api;
