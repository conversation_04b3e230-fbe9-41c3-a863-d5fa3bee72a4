// Avatar hanke teenus Bitbucketi jaoks
import { bitbucketApi } from './api';

/**
 * Avatari teenus Bitbucketi piltide hankimiseks
 */
export const avatarService = {
  
  /**
   * Hangi kasutaja avatar URL otse Bitbucketist
   * @param {string} userId - Bitbucketi kasutaja ID
   * @returns {Promise<string>} Avatari URL
   */
  async getUserAvatarUrl(userId) {
    try {
      // Kui kasutaja info on juba rakenduses olemas, siis kasuta seda
      // Muidu tee API päring Bitbucketi kasutaja info saamiseks
      const userResponse = await bitbucketApi.getUserInfo(userId);
      return userResponse?.links?.avatar?.href;
    } catch (error) {
      console.error('Viga avatari hankimisel:', error);
      return null;
    }
  },

  /**
   * Hangi tööruumi avatar URL
   * @param {string} workspaceSlug - Tööruumi slug
   * @returns {string} Tööruumi avatari URL
   */
  getWorkspaceAvatarUrl(workspaceSlug) {
    return `https://bitbucket.org/workspaces/${workspaceSlug}/avatar/`;
  },

  /**
   * Eraldi <PERSON>siani avatari URL-i hankimine Gravatari URL-ist
   * @param {string} gravatarUrl - Gravatari URL koos Atlassiani tagavaraparameetriga
   * @returns {string|null} Atlassiani avatari URL või null
   */
  extractAtlassianAvatarUrl(gravatarUrl) {
    if (!gravatarUrl || !gravatarUrl.includes('gravatar.com')) return null;
    
    try {
      const url = new URL(gravatarUrl);
      const fallbackParam = url.searchParams.get('d');
      if (fallbackParam && fallbackParam.includes('avatar-management--avatars')) {
        return decodeURIComponent(fallbackParam);
      }
    } catch (e) {
      console.log('Viga Atlassiani avatari URL-i ekstraktimisel:', e);
    }
    return null;
  },

  /**
   * Genereeri alternatiivne avatar, kui päris avatar pole saadaval
   * @param {string} name - Kasutaja/tööruumi nimi
   * @param {string} type - 'user' või 'workspace'
   * @returns {string} Genereeritud avatari URL
   */
  generateFallbackAvatarUrl(name, type = 'user') {
    if (!name) return null;
    
    const cleanName = name.replace(/[^a-zA-Z0-9\s]/g, '').trim();
    
    // Erinevad värvid erinevatele tüüpidele
    const colors = {
      workspace: { bg: '6366f1', text: 'ffffff' }, // Indigo
      user: { bg: '3b82f6', text: 'ffffff' }       // Sinine
    };
    
    const colorScheme = colors[type] || colors.user;
    
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(cleanName)}&background=${colorScheme.bg}&color=${colorScheme.text}&size=80&bold=true&format=svg`;
  }
};

export default avatarService;
