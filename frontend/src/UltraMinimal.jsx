import { useState } from 'react';

function UltraMinimal() {
  const [count, setCount] = useState(0);
  
  return (
    <div style={{ 
      padding: '40px', 
      margin: '40px auto', 
      maxWidth: '600px', 
      backgroundColor: '#ffffff',
      boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
      borderRadius: '8px',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h1 style={{ color: '#2563eb', marginBottom: '20px' }}>
        Ultra Minimal Test Component
      </h1>
      <p style={{ marginBottom: '20px' }}>
        This is a minimal test component with inline styles and no external dependencies.
      </p>
      <div style={{ marginBottom: '20px' }}>
        <p>Counter: {count}</p>
        <button 
          onClick={() => setCount(prev => prev + 1)}
          style={{
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '8px 16px',
            cursor: 'pointer',
            marginRight: '8px'
          }}
        >
          Increment
        </button>
        <button 
          onClick={() => setCount(0)}
          style={{
            backgroundColor: '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            padding: '8px 16px',
            cursor: 'pointer'
          }}
        >
          Reset
        </button>
      </div>
      <div style={{ 
        backgroundColor: '#f3f4f6', 
        padding: '16px', 
        borderRadius: '4px',
        marginTop: '20px' 
      }}>
        <p>Timestamp: {new Date().toLocaleTimeString()}</p>
        <p>React is working if you can see this component and interact with the buttons.</p>
      </div>
    </div>
  );
}

export default UltraMinimal;
