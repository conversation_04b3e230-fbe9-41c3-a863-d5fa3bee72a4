import { useState, useEffect } from 'react';

function MinimalTestApp() {
  const [message, setMessage] = useState('Loading...');
  const [apiResult, setApiResult] = useState(null);
  
  useEffect(() => {
    console.log('MinimalTestApp mounted');
    setMessage('Testing API connection...');
    
    // Direct fetch call to avoid any potential issues with the api service
    fetch('http://localhost:3002/api/health')
      .then(response => {
        console.log('API response status:', response.status);
        return response.json();
      })
      .then(data => {
        console.log('API data:', data);
        setApiResult(data);
        setMessage('API connection successful!');
      })
      .catch(error => {
        console.error('API error:', error);
        setMessage(`API connection failed: ${error.message}`);
      });
  }, []);
  
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      lineHeight: 1.5
    }}>
      <h1 style={{ color: '#333' }}>Minimal Test App</h1>
      
      <div style={{ 
        padding: '15px',
        backgroundColor: '#f5f5f5',
        borderRadius: '5px',
        marginTop: '20px',
        border: '1px solid #ddd'
      }}>
        <h2 style={{ margin: '0 0 10px 0', color: '#444' }}>Status</h2>
        <p>{message}</p>
        
        {apiResult && (
          <div style={{ 
            marginTop: '15px',
            padding: '10px',
            backgroundColor: '#e9f5ff',
            borderRadius: '4px' 
          }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#0066cc' }}>API Response</h3>
            <pre style={{ 
              backgroundColor: '#fff',
              padding: '10px',
              borderRadius: '4px',
              overflow: 'auto' 
            }}>
              {JSON.stringify(apiResult, null, 2)}
            </pre>
          </div>
        )}
      </div>
      
      <p style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        This is a minimal test app to verify that React is rendering correctly and 
        can communicate with the backend API.
      </p>
    </div>
  );
}

export default MinimalTestApp;
