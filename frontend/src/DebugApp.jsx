import { useState, useEffect } from 'react';
import { bitbucketApi } from './services/api';

function DebugApp() {
  console.log("DebugApp rendering");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [workspaces, setWorkspaces] = useState([]);
  const [apiStatus, setApiStatus] = useState('Checking API...');
  
  // Basic check to see if we can render at all
  useEffect(() => {
    document.title = 'Debug App - ' + new Date().toISOString();
    console.log('Debug App mounted');
    
    // First check API health directly with fetch to rule out module import issues
    fetch('http://localhost:3002/api/health')
      .then(res => {
        if (!res.ok) throw new Error(`Status: ${res.status}`);
        return res.json();
      })
      .then(data => {
        console.log('API Health check:', data);
        setApiStatus(`API Health OK: ${data.status}`);
      })
      .catch(err => {
        console.error('API Health error:', err);
        setApiStatus(`API Health Error: ${err.message}`);
      });
  }, []);
  
  // Try to load workspaces using the API service
  useEffect(() => {
    async function loadWorkspaces() {
      try {
        console.log("Fetching workspaces...");
        const data = await bitbucketApi.getUserWorkspaces();
        console.log("Workspaces fetched:", data);
        setWorkspaces(data.values || []);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching workspaces:", err);
        setError(err.message || "Failed to load workspaces");
        setLoading(false);
      }
    }
    
    loadWorkspaces();
  }, []);
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Bitbucket User Permission Manager</h1>
      <h2>Debug Mode</h2>
      
      <div style={{ padding: '10px', background: '#f0f0f0', borderRadius: '5px', marginBottom: '20px' }}>
        <h3>API Status</h3>
        <p>{apiStatus}</p>
      </div>
      
      {loading && <p>Loading workspaces...</p>}
      
      {error && (
        <div style={{ color: 'red', padding: '10px', border: '1px solid red', borderRadius: '5px', marginBottom: '20px' }}>
          <h3>Error</h3>
          <p>{error}</p>
        </div>
      )}
      
      {!loading && !error && (
        <div>
          <h3>Available Workspaces ({workspaces.length})</h3>
          <ul>
            {workspaces.map(workspace => (
              <li key={workspace.uuid}>
                {workspace.name} ({workspace.slug})
              </li>
            ))}
          </ul>
          
          {workspaces.length === 0 && <p>No workspaces found.</p>}
        </div>
      )}
    </div>
  );
}

export default DebugApp;
