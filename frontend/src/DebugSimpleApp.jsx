import React from 'react';

// Simple App component for debugging React rendering issues
function DebugSimpleApp() {
  const [timestamp, setTimestamp] = React.useState(new Date().toLocaleTimeString());
  
  // Update timestamp every second
  React.useEffect(() => {
    const timer = setInterval(() => {
      setTimestamp(new Date().toLocaleTimeString());
    }, 1000);
    
    // Log component mount
    console.log('DebugSimpleApp mounted at', new Date().toISOString());
    
    // Cleanup on unmount
    return () => {
      clearInterval(timer);
      console.log('DebugSimpleApp unmounted at', new Date().toISOString());
    };
  }, []);
  
  return (
    <div style={{ 
      maxWidth: '800px', 
      margin: '40px auto', 
      padding: '20px', 
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h1 style={{ color: '#2563eb' }}>Bitbucket User Permission Manager</h1>
      <p>Application is loading successfully! This confirms that React is working.</p>
      <p>Current time: <strong>{timestamp}</strong> (updates every second)</p>
      <div style={{ 
        backgroundColor: '#f1f5f9', 
        padding: '15px', 
        borderRadius: '4px',
        marginTop: '20px' 
      }}>
        <h2>Debug Information</h2>
        <p><strong>React version:</strong> {React.version}</p>
        <p><strong>React DOM element:</strong> document.getElementById('root')</p>
        <p><strong>Component:</strong> DebugSimpleApp</p>
        <button 
          onClick={() => alert('Button click confirmed! UI interaction is working.')}
          style={{
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          Test Button
        </button>
      </div>
    </div>
  );
}

export default DebugSimpleApp;
