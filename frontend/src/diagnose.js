/*
 * This is a diagnostic script to help troubleshoot React rendering issues
 * Run it with: node diagnose.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function diagnose() {
  console.log('====== REACT RENDERING DIAGNOSTIC TOOL ======');
  console.log('Current time:', new Date().toISOString());
  console.log('\n');

  try {
    // Check Node.js and npm versions
    const nodeVersion = execSync('node --version').toString().trim();
    const npmVersion = execSync('npm --version').toString().trim();
    console.log('Node.js version:', nodeVersion);
    console.log('npm version:', npmVersion);

    // Check package.json
    const frontendDir = path.resolve(__dirname, '..');
    const packagePath = path.join(frontendDir, 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      console.log('\nDependencies:');
      console.log('- React:', packageJson.dependencies.react);
      console.log('- React DOM:', packageJson.dependencies['react-dom']);
      console.log('- Vite:', packageJson.devDependencies.vite);
    } else {
      console.log('package.json not found at', packagePath);
    }

    // Check HTML
    const htmlPath = path.join(frontendDir, 'index.html');
    if (fs.existsSync(htmlPath)) {
      const html = fs.readFileSync(htmlPath, 'utf8');
      console.log('\nChecking index.html:');
      console.log('- Contains #root element:', html.includes('id="root"'));
      console.log('- Script source:', html.includes('src="/src/main.jsx"') ? '/src/main.jsx' : 'Unknown');
    } else {
      console.log('index.html not found at', htmlPath);
    }

    // Check main.jsx
    const mainJsxPath = path.join(__dirname, 'main.jsx');
    if (fs.existsSync(mainJsxPath)) {
      const mainJsx = fs.readFileSync(mainJsxPath, 'utf8');
      console.log('\nChecking main.jsx:');
      console.log('- Imports React:', mainJsx.includes("import React from 'react'"));
      console.log('- Imports ReactDOM:', mainJsx.includes("import ReactDOM from 'react-dom/client'"));
      console.log('- Uses createRoot:', mainJsx.includes("createRoot"));
      console.log('- Uses StrictMode:', mainJsx.includes("<React.StrictMode>"));
    } else {
      console.log('main.jsx not found at', mainJsxPath);
    }

    // Check Vite config
    const viteConfigPath = path.join(frontendDir, 'vite.config.js');
    if (fs.existsSync(viteConfigPath)) {
      const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
      console.log('\nChecking vite.config.js:');
      console.log('- Uses react plugin:', viteConfig.includes("react()"));
    } else {
      console.log('vite.config.js not found at', viteConfigPath);
    }

    // Process list to check if servers are running
    console.log('\nChecking for running servers:');
    try {
      const processes = execSync('ps aux | grep "[n]ode\\|[v]ite\\|[s]erver"').toString();
      console.log(processes);
    } catch (error) {
      console.log('No relevant processes found or error listing processes');
    }

    // Check ports
    console.log('\nChecking open ports:');
    try {
      const ports = execSync('lsof -i:3002,5173,5174,5175,5176,5177,8000,8080').toString();
      console.log(ports);
    } catch (error) {
      console.log('No relevant ports found or error listing ports');
    }

    console.log('\n====== DIAGNOSTIC COMPLETE ======');
  } catch (error) {
    console.error('Error during diagnostics:', error);
  }
}

diagnose();
