<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Bitbucket API Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 { color: #2563eb; }
        pre {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 4px;
            overflow: auto;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { color: #16a34a; }
        .error { color: #dc2626; }
    </style>
</head>
<body>
    <h1>Bitbucket Permission Manager - Direct Test</h1>
    <p>This page tests the Bitbucket API without React or any build tools.</p>
    
    <div class="panel">
        <h2>1. Backend Health Check</h2>
        <button id="healthBtn">Test API Health</button>
        <pre id="healthResult">Click the button to test...</pre>
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>2. Workspaces List</h2>
            <button id="workspacesBtn">Get Workspaces</button>
            <pre id="workspacesResult">Click the button to test...</pre>
        </div>
        
        <div class="panel">
            <h2>3. Workspace Details</h2>
            <div>
                <input type="text" id="workspaceInput" value="ovaal" placeholder="Workspace name">
                <button id="workspaceBtn">Get Details</button>
            </div>
            <pre id="workspaceResult">Click the button to test...</pre>
        </div>
    </div>
    
    <div class="panel">
        <h2>4. Console Log</h2>
        <pre id="consoleLog">Log messages will appear here...</pre>
    </div>
    
    <script>
        // API base URL
        const API_BASE = 'http://localhost:3002/api';
        let logMessages = [];
        
        // Log function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logMessages.push(`[${timestamp}] ${message}`);
            document.getElementById('consoleLog').textContent = logMessages.join('\n');
            console.log(message);
        }
        
        // Generic fetch function with error handling
        async function fetchApi(endpoint, resultElement) {
            const url = `${API_BASE}${endpoint}`;
            log(`Fetching: ${url}`);
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    log(`Success: ${endpoint} (${response.status})`);
                    document.getElementById(resultElement).textContent = JSON.stringify(data, null, 2);
                    document.getElementById(resultElement).className = 'success';
                } else {
                    log(`Error: ${endpoint} (${response.status}) - ${response.statusText}`);
                    document.getElementById(resultElement).textContent = `Error ${response.status}: ${JSON.stringify(data, null, 2)}`;
                    document.getElementById(resultElement).className = 'error';
                }
                
                return data;
            } catch (error) {
                log(`Exception: ${endpoint} - ${error.message}`);
                document.getElementById(resultElement).textContent = `Error: ${error.message}`;
                document.getElementById(resultElement).className = 'error';
                return null;
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Page loaded at ' + new Date().toLocaleString());
            
            // Health check
            document.getElementById('healthBtn').addEventListener('click', () => {
                fetchApi('/health', 'healthResult');
            });
            
            // Workspaces
            document.getElementById('workspacesBtn').addEventListener('click', () => {
                fetchApi('/bitbucket/workspaces', 'workspacesResult');
            });
            
            // Workspace details
            document.getElementById('workspaceBtn').addEventListener('click', () => {
                const workspace = document.getElementById('workspaceInput').value;
                if (!workspace) {
                    alert('Please enter a workspace name');
                    return;
                }
                fetchApi(`/bitbucket/workspace/${workspace}`, 'workspaceResult');
            });
            
            // Automatic health check on load
            setTimeout(() => {
                document.getElementById('healthBtn').click();
            }, 500);
        });
    </script>
</body>
</html>
