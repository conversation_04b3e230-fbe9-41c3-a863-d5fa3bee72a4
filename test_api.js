// Test script to check API connection
const fetch = require('node-fetch');

async function testApi() {
  try {
    console.log('Testing API health endpoint...');
    const healthResponse = await fetch('http://localhost:3002/api/health');
    const healthData = await healthResponse.json();
    console.log('Health endpoint response:', healthData);
    
    console.log('\nTesting workspaces endpoint...');
    const workspacesResponse = await fetch('http://localhost:3002/api/bitbucket/workspaces');
    const workspacesData = await workspacesResponse.json();
    console.log('Number of workspaces:', workspacesData.values?.length || 0);
    console.log('First workspace:', workspacesData.values?.[0]?.name || 'None');
  } catch (error) {
    console.error('Error testing API:', error.message);
  }
}

testApi();
