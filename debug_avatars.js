#!/usr/bin/env node

const axios = require('axios');
require('dotenv').config({ path: './backend/.env' });

async function debugAvatars() {
  try {
    const token = process.env.BITBUCKET_API_TOKEN;
    if (!token) {
      console.error('BITBUCKET_API_TOKEN not found in backend/.env');
      process.exit(1);
    }

    const username = 'Hennot'; // From bitbucketApi.js
    const credentials = Buffer.from(`${username}:${token}`).toString('base64');

    const client = axios.create({
      baseURL: 'https://api.bitbucket.org/2.0',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log('🔍 Fetching workspaces to find users...');
    const workspacesResponse = await client.get('/workspaces?pagelen=10');
    const workspaces = workspacesResponse.data.values;
    
    if (workspaces.length === 0) {
      console.log('❌ No workspaces found');
      return;
    }

    console.log(`✅ Found ${workspaces.length} workspaces`);
    console.log('\n🔍 Checking workspace avatar structure:');
    
    const firstWorkspace = workspaces[0];
    console.log('Workspace object keys:', Object.keys(firstWorkspace));
    console.log('Workspace links:', firstWorkspace.links);
    if (firstWorkspace.links && firstWorkspace.links.avatar) {
      console.log('✅ Workspace avatar URL:', firstWorkspace.links.avatar.href);
    } else {
      console.log('❌ No avatar found in workspace links');
    }

    // Get workspace members to find users
    console.log('\n🔍 Fetching workspace members...');
    const membersResponse = await client.get(`/workspaces/${firstWorkspace.slug}/members?pagelen=5`);
    const members = membersResponse.data.values;

    if (members.length === 0) {
      console.log('❌ No members found in workspace');
      return;
    }

    console.log(`✅ Found ${members.length} members`);
    
    for (let i = 0; i < Math.min(3, members.length); i++) {
      const member = members[i];
      console.log(`\n🔍 Member ${i + 1} user object:`, JSON.stringify(member.user, null, 2));
      
      if (member.user && (member.user.uuid || member.user.account_id)) {
        try {
          console.log(`\n🔍 Fetching full user info for: ${member.user.display_name || member.user.nickname}`);
          const userId = member.user.uuid || member.user.account_id;
          const cleanId = userId.replace(/[{}]/g, '');
          
          const userResponse = await client.get(`/users/${cleanId}`);
          const fullUser = userResponse.data;
          
          console.log('User object keys:', Object.keys(fullUser));
          console.log('User links:', fullUser.links);
          
          if (fullUser.links && fullUser.links.avatar) {
            console.log('✅ User avatar URL:', fullUser.links.avatar.href);
            
            // Test if the avatar URL is accessible
            try {
              const avatarResponse = await axios.head(fullUser.links.avatar.href);
              console.log('✅ Avatar URL is accessible, Content-Type:', avatarResponse.headers['content-type']);
            } catch (avatarError) {
              console.log('❌ Avatar URL not accessible:', avatarError.message);
            }
          } else {
            console.log('❌ No avatar found in user links');
          }
        } catch (userError) {
          console.log('❌ Error fetching user info:', userError.message);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

debugAvatars();
