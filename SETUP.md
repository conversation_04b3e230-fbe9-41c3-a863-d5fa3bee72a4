# Seadistamise juhend

## Eeltingimused

- Node.js (versioon 18 või uuem)
- npm või yarn
- Bitbucket App Password vajalike õigustega

## Bitbucket API võtme loomine

1. Mine Bitbucket'i seadetesse: https://bitbucket.org/account/settings/
2. Vali "App passwords" vasakult menüüst
3. K<PERSON>i "Create app password"
4. <PERSON> (nt. "Permission Manager")
5. <PERSON><PERSON> järgmised õigused:
   - **Account**: Read
   - **Workspace membership**: Read, Write
   - **Repositories**: Admin
   - **Projects**: Read

6. <PERSON><PERSON><PERSON> genereeritud võti (see kuvatakse ainult üks kord!)

## Projekti seadistamine

### 1. Klooni või laadi alla projekt

```bash
git clone <repository-url>
cd bitbucket-user-permission-manager
```

### 2. Installi sõltuvused

```bash
npm run install-all
```

### 3. Seadista keskkonnamuutujad

<PERSON> `.env.example` failist `.env` fail backend kausta:

```bash
cp backend/.env.example backend/.env
```

Muuda `backend/.env` failis API võtit:

```
BITBUCKET_API_TOKEN=ATBB_your_actual_api_token_here
PORT=3001
NODE_ENV=development
```

⚠️ **Oluline**: Asenda `ATBB_your_actual_api_token_here` oma tegeliku Bitbucket API võtmega!

### 4. Käivita rakendus

```bash
npm run dev
```

See käivitab:
- Backend serveri pordil 3001
- Frontend rakenduse pordil 5173

### 5. Ava brauseris

Mine aadressile: http://localhost:5173

## Kasutamine

1. Sisesta workspace nimi (nt. "minu-ettevõte")
2. Kliki "Vaata õigusi"
3. Vaata kasutajate nimekirja koos õigustega
4. Kasuta otsingut ja filtreid
5. Eemalda õigusi "Eemalda" nuppudega

## Tõrkeotsing

### "API võti ei leitud" viga
- Kontrolli, et `.env` fail on backend kaustas
- Veendu, et API võti on õigesti kopeeritud

### "Ligipääs keelatud" viga
- Kontrolli API võtme õigusi
- Veendu, et sul on ligipääs workspace'ile

### "Workspace ei leitud" viga
- Kontrolli workspace nime õigekirja
- Veendu, et workspace eksisteerib ja sul on sellele ligipääs

## Turvalisus

⚠️ **Hoiatus**: 
- Hoia API võtit turvaliselt
- Ära jaga `.env` faili
- Kasuta tööriista ettevaatlikult - õiguste eemaldamine on pöördumatu

## Arendamine

### Backend
```bash
cd backend
npm run dev
```

### Frontend
```bash
cd frontend
npm run dev
```

### Uue funktsionaalsuse lisamine

1. Lisa uus endpoint `backend/routes/bitbucket.js` faili
2. Lisa vajalik meetod `backend/services/bitbucketApi.js` faili
3. Uuenda frontend API teenust `frontend/src/services/api.js`
4. Lisa uus komponent või uuenda olemasolevaid
