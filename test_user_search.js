#!/usr/bin/env node

/**
 * Test script for the Global User Search functionality
 * This script tests the new user search feature implementation
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002/api';

// Test the backend API endpoints
async function testBackendAPI() {
  console.log('🧪 Testing Backend API endpoints...\n');
  
  try {
    // Test workspace list
    console.log('1️⃣ Testing workspace list...');
    const workspacesResponse = await axios.get(`${API_BASE_URL}/bitbucket/workspaces`);
    console.log(`✅ Found ${workspacesResponse.data.values?.length || 0} workspaces`);
    
    if (workspacesResponse.data.values && workspacesResponse.data.values.length > 0) {
      const firstWorkspace = workspacesResponse.data.values[0];
      console.log(`📁 First workspace: ${firstWorkspace.name} (${firstWorkspace.slug})`);
      
      // Test workspace overview (includes permissions)
      console.log('\n2️⃣ Testing workspace overview...');
      const overviewResponse = await axios.get(`${API_BASE_URL}/bitbucket/workspace/${firstWorkspace.slug}/overview`);
      const permissions = overviewResponse.data.permissions || [];
      console.log(`✅ Found ${permissions.length} permissions in ${firstWorkspace.name}`);
      
      // Show some sample users if available
      if (permissions.length > 0) {
        console.log('\n👥 Sample users found:');
        const uniqueUsers = new Map();
        permissions.forEach(perm => {
          if (perm.user) {
            const userId = perm.user.uuid || perm.user.account_id;
            if (!uniqueUsers.has(userId)) {
              uniqueUsers.set(userId, perm.user);
            }
          }
        });
        
        Array.from(uniqueUsers.values()).slice(0, 3).forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.display_name || user.nickname || user.username} (@${user.username})`);
        });
        
        console.log(`   ... and ${uniqueUsers.size - 3} more users`);
      }
    }
    
    console.log('\n✅ Backend API tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Backend API test failed:', error.response?.data || error.message);
  }
}

// Test frontend availability
async function testFrontend() {
  console.log('\n🖥️  Testing Frontend availability...\n');
  
  try {
    const response = await axios.get('http://localhost:5174');
    console.log('✅ Frontend is accessible');
    console.log('📱 Application should be available at: http://localhost:5174');
  } catch (error) {
    console.error('❌ Frontend test failed:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Global User Search - Testing Suite\n');
  console.log('=======================================\n');
  
  await testBackendAPI();
  await testFrontend();
  
  console.log('\n📝 Testing Instructions:');
  console.log('1. Open http://localhost:5174 in your browser');
  console.log('2. Click the "Filter" button to expand advanced filters');
  console.log('3. Find the "Global User Search" section');
  console.log('4. Type a user name (at least 2 characters) in the search box');
  console.log('5. Watch as users appear live while typing');
  console.log('6. Select users and test bulk operations');
  console.log('7. Try removing permissions from individual repositories or entire workspaces');
  
  console.log('\n🎯 Expected Features:');
  console.log('• Live search across all workspaces');
  console.log('• Search results sorted alphabetically');
  console.log('• User selection with checkboxes');
  console.log('• Bulk operations (select all, clear selection)');
  console.log('• Individual repository permission removal');
  console.log('• Workspace-wide user removal');
  console.log('• Real-time search results as you type');
  
  console.log('\n✨ Test completed! Check the browser to verify functionality.');
}

// Run the tests
runTests().catch(console.error);
